{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cantieri\\\\PasswordManagementDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Box, Typography, Alert, IconButton, InputAdornment, Tabs, Tab, Divider } from '@mui/material';\nimport { Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Lock as LockIcon, Key as KeyIcon, ContentCopy as ContentCopyIcon } from '@mui/icons-material';\nimport cantieriService from '../../services/cantieriService';\nimport HoldToViewButton from './HoldToViewButton';\n\n/**\n * Dialog per la gestione delle password del cantiere\n * Permette di visualizzare e modificare la password\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PasswordManagementDialog = ({\n  open,\n  onClose,\n  cantiere,\n  onPasswordChanged = null\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stati per visualizzazione password\n  const [viewPasswordData, setViewPasswordData] = useState({\n    passwordAttuale: '',\n    passwordRivelata: '',\n    showPassword: false\n  });\n\n  // Stati per cambio password\n  const [changePasswordData, setChangePasswordData] = useState({\n    passwordAttuale: '',\n    passwordNuova: '',\n    confermaPassword: '',\n    showCurrentPassword: false,\n    showNewPassword: false,\n    showConfirmPassword: false\n  });\n\n  // Reset dei dati quando si apre/chiude il dialog\n  React.useEffect(() => {\n    if (open) {\n      setActiveTab(0);\n      setError('');\n      setSuccess('');\n      setViewPasswordData({\n        passwordAttuale: '',\n        passwordRivelata: '',\n        showPassword: false\n      });\n      setChangePasswordData({\n        passwordAttuale: '',\n        passwordNuova: '',\n        confermaPassword: '',\n        showCurrentPassword: false,\n        showNewPassword: false,\n        showConfirmPassword: false\n      });\n    }\n  }, [open]);\n\n  // Gestisce il cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Gestisce la visualizzazione della password\n  const handleViewPassword = async () => {\n    if (!viewPasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const response = await cantieriService.verifyCantierePassword(cantiere.id_cantiere, viewPasswordData.passwordAttuale);\n      if (response.password_corretta) {\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: response.password_cantiere,\n          showPassword: true\n        }));\n        setSuccess('Password verificata correttamente');\n      } else {\n        setError('Password non corretta');\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: '',\n          showPassword: false\n        }));\n      }\n    } catch (err) {\n      console.error('Errore nella verifica password:', err);\n      setError(err.detail || 'Errore nella verifica della password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce il cambio password\n  const handleChangePassword = async () => {\n    // Validazioni\n    if (!changePasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n    if (!changePasswordData.passwordNuova.trim()) {\n      setError('Inserisci la nuova password');\n      return;\n    }\n    if (changePasswordData.passwordNuova !== changePasswordData.confermaPassword) {\n      setError('Le nuove password non coincidono');\n      return;\n    }\n    if (changePasswordData.passwordNuova.length < 3) {\n      setError('La nuova password deve essere di almeno 3 caratteri');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const response = await cantieriService.changeCantierePassword(cantiere.id_cantiere, changePasswordData.passwordAttuale, changePasswordData.passwordNuova, changePasswordData.confermaPassword);\n      if (response.success) {\n        setSuccess('Password cambiata con successo!');\n        setChangePasswordData({\n          passwordAttuale: '',\n          passwordNuova: '',\n          confermaPassword: '',\n          showCurrentPassword: false,\n          showNewPassword: false,\n          showConfirmPassword: false\n        });\n\n        // Notifica il componente padre\n        if (onPasswordChanged) {\n          onPasswordChanged();\n        }\n      } else {\n        setError(response.message || 'Errore nel cambio password');\n      }\n    } catch (err) {\n      console.error('Errore nel cambio password:', err);\n      setError(err.detail || 'Errore nel cambio password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Copia la password negli appunti\n  const handleCopyPassword = () => {\n    if (viewPasswordData.passwordRivelata) {\n      navigator.clipboard.writeText(viewPasswordData.passwordRivelata);\n      setSuccess('Password copiata negli appunti');\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleClose = () => {\n    setError('');\n    setSuccess('');\n    onClose();\n  };\n  if (!cantiere) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(LockIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Gestione Password - \", cantiere.nome]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 21\n            }, this),\n            label: \"Visualizza Password\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(KeyIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 21\n            }, this),\n            label: \"Cambia Password\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Inserisci la password attuale per visualizzarla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Password Attuale\",\n          type: viewPasswordData.showPassword ? 'text' : 'password',\n          value: viewPasswordData.passwordAttuale,\n          onChange: e => setViewPasswordData(prev => ({\n            ...prev,\n            passwordAttuale: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setViewPasswordData(prev => ({\n                  ...prev,\n                  showPassword: !prev.showPassword\n                })),\n                edge: \"end\",\n                children: viewPasswordData.showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 56\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 80\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), viewPasswordData.passwordRivelata && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            p: 2,\n            bgcolor: 'success.light',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Password del Cantiere:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontFamily: 'monospace',\n                bgcolor: 'background.paper',\n                p: 1,\n                borderRadius: 1,\n                flex: 1\n              },\n              children: viewPasswordData.passwordRivelata\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCopyPassword,\n              title: \"Copia password\",\n              children: /*#__PURE__*/_jsxDEV(ContentCopyIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleViewPassword,\n            disabled: loading || !viewPasswordData.passwordAttuale.trim(),\n            startIcon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 28\n            }, this),\n            children: loading ? 'Verifica...' : 'Visualizza Password'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Inserisci la password attuale e la nuova password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Password Attuale\",\n          type: changePasswordData.showCurrentPassword ? 'text' : 'password',\n          value: changePasswordData.passwordAttuale,\n          onChange: e => setChangePasswordData(prev => ({\n            ...prev,\n            passwordAttuale: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setChangePasswordData(prev => ({\n                  ...prev,\n                  showCurrentPassword: !prev.showCurrentPassword\n                })),\n                edge: \"end\",\n                children: changePasswordData.showCurrentPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 65\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Nuova Password\",\n          type: changePasswordData.showNewPassword ? 'text' : 'password',\n          value: changePasswordData.passwordNuova,\n          onChange: e => setChangePasswordData(prev => ({\n            ...prev,\n            passwordNuova: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setChangePasswordData(prev => ({\n                  ...prev,\n                  showNewPassword: !prev.showNewPassword\n                })),\n                edge: \"end\",\n                children: changePasswordData.showNewPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 61\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 85\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Conferma Nuova Password\",\n          type: changePasswordData.showConfirmPassword ? 'text' : 'password',\n          value: changePasswordData.confermaPassword,\n          onChange: e => setChangePasswordData(prev => ({\n            ...prev,\n            confermaPassword: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setChangePasswordData(prev => ({\n                  ...prev,\n                  showConfirmPassword: !prev.showConfirmPassword\n                })),\n                edge: \"end\",\n                children: changePasswordData.showConfirmPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 65\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleChangePassword,\n            disabled: loading || !changePasswordData.passwordAttuale.trim() || !changePasswordData.passwordNuova.trim(),\n            startIcon: /*#__PURE__*/_jsxDEV(KeyIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 28\n            }, this),\n            children: loading ? 'Cambio...' : 'Cambia Password'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        children: \"Chiudi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordManagementDialog, \"OfmEJ5Yi0eqw12JlRV3NXlgCVok=\");\n_c = PasswordManagementDialog;\nexport default PasswordManagementDialog;\nvar _c;\n$RefreshReg$(_c, \"PasswordManagementDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Box", "Typography", "<PERSON><PERSON>", "IconButton", "InputAdornment", "Tabs", "Tab", "Divider", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "Lock", "LockIcon", "Key", "KeyIcon", "ContentCopy", "ContentCopyIcon", "cantieriService", "HoldToViewButton", "jsxDEV", "_jsxDEV", "PasswordManagementDialog", "open", "onClose", "cantiere", "onPasswordChanged", "_s", "activeTab", "setActiveTab", "loading", "setLoading", "error", "setError", "success", "setSuccess", "viewPasswordData", "setViewPasswordData", "passwordAttuale", "passwordRivelata", "showPassword", "changePasswordData", "setChangePasswordData", "passwordNuova", "confermaPassword", "showCurrentPassword", "showNewPassword", "showConfirmPassword", "useEffect", "handleTabChange", "event", "newValue", "handleViewPassword", "trim", "response", "verifyCantierePassword", "id_cantiere", "password_corretta", "prev", "password_cantiere", "err", "console", "detail", "handleChangePassword", "length", "changeCantierePassword", "message", "handleCopyPassword", "navigator", "clipboard", "writeText", "handleClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "sx", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "nome", "borderBottom", "borderColor", "mb", "value", "onChange", "icon", "label", "iconPosition", "severity", "color", "type", "e", "target", "InputProps", "endAdornment", "position", "onClick", "edge", "mt", "p", "bgcolor", "borderRadius", "gutterBottom", "fontFamily", "flex", "title", "justifyContent", "disabled", "startIcon", "my", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cantieri/PasswordManagementDialog.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  <PERSON>alogActions,\n  Button,\n  TextField,\n  Box,\n  Typography,\n  Alert,\n  IconButton,\n  InputAdornment,\n  Tabs,\n  Tab,\n  Divider\n} from '@mui/material';\nimport {\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Lock as LockIcon,\n  Key as KeyIcon,\n  ContentCopy as ContentCopyIcon\n} from '@mui/icons-material';\nimport cantieriService from '../../services/cantieriService';\nimport HoldToViewButton from './HoldToViewButton';\n\n/**\n * Dialog per la gestione delle password del cantiere\n * Permette di visualizzare e modificare la password\n */\nconst PasswordManagementDialog = ({\n  open,\n  onClose,\n  cantiere,\n  onPasswordChanged = null\n}) => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  \n  // Stati per visualizzazione password\n  const [viewPasswordData, setViewPasswordData] = useState({\n    passwordAttuale: '',\n    passwordRivelata: '',\n    showPassword: false\n  });\n  \n  // Stati per cambio password\n  const [changePasswordData, setChangePasswordData] = useState({\n    passwordAttuale: '',\n    passwordNuova: '',\n    confermaPassword: '',\n    showCurrentPassword: false,\n    showNewPassword: false,\n    showConfirmPassword: false\n  });\n\n  // Reset dei dati quando si apre/chiude il dialog\n  React.useEffect(() => {\n    if (open) {\n      setActiveTab(0);\n      setError('');\n      setSuccess('');\n      setViewPasswordData({\n        passwordAttuale: '',\n        passwordRivelata: '',\n        showPassword: false\n      });\n      setChangePasswordData({\n        passwordAttuale: '',\n        passwordNuova: '',\n        confermaPassword: '',\n        showCurrentPassword: false,\n        showNewPassword: false,\n        showConfirmPassword: false\n      });\n    }\n  }, [open]);\n\n  // Gestisce il cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Gestisce la visualizzazione della password\n  const handleViewPassword = async () => {\n    if (!viewPasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    \n    try {\n      const response = await cantieriService.verifyCantierePassword(\n        cantiere.id_cantiere,\n        viewPasswordData.passwordAttuale\n      );\n      \n      if (response.password_corretta) {\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: response.password_cantiere,\n          showPassword: true\n        }));\n        setSuccess('Password verificata correttamente');\n      } else {\n        setError('Password non corretta');\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: '',\n          showPassword: false\n        }));\n      }\n    } catch (err) {\n      console.error('Errore nella verifica password:', err);\n      setError(err.detail || 'Errore nella verifica della password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce il cambio password\n  const handleChangePassword = async () => {\n    // Validazioni\n    if (!changePasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n    \n    if (!changePasswordData.passwordNuova.trim()) {\n      setError('Inserisci la nuova password');\n      return;\n    }\n    \n    if (changePasswordData.passwordNuova !== changePasswordData.confermaPassword) {\n      setError('Le nuove password non coincidono');\n      return;\n    }\n    \n    if (changePasswordData.passwordNuova.length < 3) {\n      setError('La nuova password deve essere di almeno 3 caratteri');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    \n    try {\n      const response = await cantieriService.changeCantierePassword(\n        cantiere.id_cantiere,\n        changePasswordData.passwordAttuale,\n        changePasswordData.passwordNuova,\n        changePasswordData.confermaPassword\n      );\n      \n      if (response.success) {\n        setSuccess('Password cambiata con successo!');\n        setChangePasswordData({\n          passwordAttuale: '',\n          passwordNuova: '',\n          confermaPassword: '',\n          showCurrentPassword: false,\n          showNewPassword: false,\n          showConfirmPassword: false\n        });\n        \n        // Notifica il componente padre\n        if (onPasswordChanged) {\n          onPasswordChanged();\n        }\n      } else {\n        setError(response.message || 'Errore nel cambio password');\n      }\n    } catch (err) {\n      console.error('Errore nel cambio password:', err);\n      setError(err.detail || 'Errore nel cambio password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Copia la password negli appunti\n  const handleCopyPassword = () => {\n    if (viewPasswordData.passwordRivelata) {\n      navigator.clipboard.writeText(viewPasswordData.passwordRivelata);\n      setSuccess('Password copiata negli appunti');\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleClose = () => {\n    setError('');\n    setSuccess('');\n    onClose();\n  };\n\n  if (!cantiere) return null;\n\n  return (\n    <Dialog \n      open={open} \n      onClose={handleClose}\n      maxWidth=\"sm\"\n      fullWidth\n    >\n      <DialogTitle>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <LockIcon />\n          <Typography variant=\"h6\">\n            Gestione Password - {cantiere.nome}\n          </Typography>\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>\n          <Tabs value={activeTab} onChange={handleTabChange}>\n            <Tab \n              icon={<VisibilityIcon />} \n              label=\"Visualizza Password\" \n              iconPosition=\"start\"\n            />\n            <Tab \n              icon={<KeyIcon />} \n              label=\"Cambia Password\" \n              iconPosition=\"start\"\n            />\n          </Tabs>\n        </Box>\n\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n        \n        {success && (\n          <Alert severity=\"success\" sx={{ mb: 2 }}>\n            {success}\n          </Alert>\n        )}\n\n        {/* Tab Visualizza Password */}\n        {activeTab === 0 && (\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              Inserisci la password attuale per visualizzarla\n            </Typography>\n            \n            <TextField\n              fullWidth\n              label=\"Password Attuale\"\n              type={viewPasswordData.showPassword ? 'text' : 'password'}\n              value={viewPasswordData.passwordAttuale}\n              onChange={(e) => setViewPasswordData(prev => ({\n                ...prev,\n                passwordAttuale: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setViewPasswordData(prev => ({\n                        ...prev,\n                        showPassword: !prev.showPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {viewPasswordData.showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            {viewPasswordData.passwordRivelata && (\n              <Box sx={{ mt: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Password del Cantiere:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                  <Typography \n                    variant=\"h6\" \n                    sx={{ \n                      fontFamily: 'monospace',\n                      bgcolor: 'background.paper',\n                      p: 1,\n                      borderRadius: 1,\n                      flex: 1\n                    }}\n                  >\n                    {viewPasswordData.passwordRivelata}\n                  </Typography>\n                  <IconButton \n                    onClick={handleCopyPassword}\n                    title=\"Copia password\"\n                  >\n                    <ContentCopyIcon />\n                  </IconButton>\n                </Box>\n              </Box>\n            )}\n            \n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                onClick={handleViewPassword}\n                disabled={loading || !viewPasswordData.passwordAttuale.trim()}\n                startIcon={<VisibilityIcon />}\n              >\n                {loading ? 'Verifica...' : 'Visualizza Password'}\n              </Button>\n            </Box>\n          </Box>\n        )}\n\n        {/* Tab Cambia Password */}\n        {activeTab === 1 && (\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              Inserisci la password attuale e la nuova password\n            </Typography>\n            \n            <TextField\n              fullWidth\n              label=\"Password Attuale\"\n              type={changePasswordData.showCurrentPassword ? 'text' : 'password'}\n              value={changePasswordData.passwordAttuale}\n              onChange={(e) => setChangePasswordData(prev => ({\n                ...prev,\n                passwordAttuale: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setChangePasswordData(prev => ({\n                        ...prev,\n                        showCurrentPassword: !prev.showCurrentPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {changePasswordData.showCurrentPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            <Divider sx={{ my: 2 }} />\n            \n            <TextField\n              fullWidth\n              label=\"Nuova Password\"\n              type={changePasswordData.showNewPassword ? 'text' : 'password'}\n              value={changePasswordData.passwordNuova}\n              onChange={(e) => setChangePasswordData(prev => ({\n                ...prev,\n                passwordNuova: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setChangePasswordData(prev => ({\n                        ...prev,\n                        showNewPassword: !prev.showNewPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {changePasswordData.showNewPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            <TextField\n              fullWidth\n              label=\"Conferma Nuova Password\"\n              type={changePasswordData.showConfirmPassword ? 'text' : 'password'}\n              value={changePasswordData.confermaPassword}\n              onChange={(e) => setChangePasswordData(prev => ({\n                ...prev,\n                confermaPassword: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setChangePasswordData(prev => ({\n                        ...prev,\n                        showConfirmPassword: !prev.showConfirmPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {changePasswordData.showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                onClick={handleChangePassword}\n                disabled={loading || !changePasswordData.passwordAttuale.trim() || !changePasswordData.passwordNuova.trim()}\n                startIcon={<KeyIcon />}\n              >\n                {loading ? 'Cambio...' : 'Cambia Password'}\n              </Button>\n            </Box>\n          </Box>\n        )}\n      </DialogContent>\n      \n      <DialogActions>\n        <Button onClick={handleClose}>\n          Chiudi\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default PasswordManagementDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,GAAG,EACHC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,EAClCC,IAAI,IAAIC,QAAQ,EAChBC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,gBAAgB,MAAM,oBAAoB;;AAEjD;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,wBAAwB,GAAGA,CAAC;EAChCC,IAAI;EACJC,OAAO;EACPC,QAAQ;EACRC,iBAAiB,GAAG;AACtB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC;IACvD6C,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjD,QAAQ,CAAC;IAC3D6C,eAAe,EAAE,EAAE;IACnBK,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,mBAAmB,EAAE,KAAK;IAC1BC,eAAe,EAAE,KAAK;IACtBC,mBAAmB,EAAE;EACvB,CAAC,CAAC;;EAEF;EACAvD,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,IAAIzB,IAAI,EAAE;MACRM,YAAY,CAAC,CAAC,CAAC;MACfI,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;MACdE,mBAAmB,CAAC;QAClBC,eAAe,EAAE,EAAE;QACnBC,gBAAgB,EAAE,EAAE;QACpBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFE,qBAAqB,CAAC;QACpBJ,eAAe,EAAE,EAAE;QACnBK,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBC,mBAAmB,EAAE,KAAK;QAC1BC,eAAe,EAAE,KAAK;QACtBC,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACxB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM0B,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CtB,YAAY,CAACsB,QAAQ,CAAC;IACtBlB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;;EAED;EACA,MAAMiB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAChB,gBAAgB,CAACE,eAAe,CAACe,IAAI,CAAC,CAAC,EAAE;MAC5CpB,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMpC,eAAe,CAACqC,sBAAsB,CAC3D9B,QAAQ,CAAC+B,WAAW,EACpBpB,gBAAgB,CAACE,eACnB,CAAC;MAED,IAAIgB,QAAQ,CAACG,iBAAiB,EAAE;QAC9BpB,mBAAmB,CAACqB,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPnB,gBAAgB,EAAEe,QAAQ,CAACK,iBAAiB;UAC5CnB,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;QACHL,UAAU,CAAC,mCAAmC,CAAC;MACjD,CAAC,MAAM;QACLF,QAAQ,CAAC,uBAAuB,CAAC;QACjCI,mBAAmB,CAACqB,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPnB,gBAAgB,EAAE,EAAE;UACpBC,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,iCAAiC,EAAE4B,GAAG,CAAC;MACrD3B,QAAQ,CAAC2B,GAAG,CAACE,MAAM,IAAI,sCAAsC,CAAC;IAChE,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAACtB,kBAAkB,CAACH,eAAe,CAACe,IAAI,CAAC,CAAC,EAAE;MAC9CpB,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEA,IAAI,CAACQ,kBAAkB,CAACE,aAAa,CAACU,IAAI,CAAC,CAAC,EAAE;MAC5CpB,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACF;IAEA,IAAIQ,kBAAkB,CAACE,aAAa,KAAKF,kBAAkB,CAACG,gBAAgB,EAAE;MAC5EX,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;IAEA,IAAIQ,kBAAkB,CAACE,aAAa,CAACqB,MAAM,GAAG,CAAC,EAAE;MAC/C/B,QAAQ,CAAC,qDAAqD,CAAC;MAC/D;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMpC,eAAe,CAAC+C,sBAAsB,CAC3DxC,QAAQ,CAAC+B,WAAW,EACpBf,kBAAkB,CAACH,eAAe,EAClCG,kBAAkB,CAACE,aAAa,EAChCF,kBAAkB,CAACG,gBACrB,CAAC;MAED,IAAIU,QAAQ,CAACpB,OAAO,EAAE;QACpBC,UAAU,CAAC,iCAAiC,CAAC;QAC7CO,qBAAqB,CAAC;UACpBJ,eAAe,EAAE,EAAE;UACnBK,aAAa,EAAE,EAAE;UACjBC,gBAAgB,EAAE,EAAE;UACpBC,mBAAmB,EAAE,KAAK;UAC1BC,eAAe,EAAE,KAAK;UACtBC,mBAAmB,EAAE;QACvB,CAAC,CAAC;;QAEF;QACA,IAAIrB,iBAAiB,EAAE;UACrBA,iBAAiB,CAAC,CAAC;QACrB;MACF,CAAC,MAAM;QACLO,QAAQ,CAACqB,QAAQ,CAACY,OAAO,IAAI,4BAA4B,CAAC;MAC5D;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,6BAA6B,EAAE4B,GAAG,CAAC;MACjD3B,QAAQ,CAAC2B,GAAG,CAACE,MAAM,IAAI,4BAA4B,CAAC;IACtD,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI/B,gBAAgB,CAACG,gBAAgB,EAAE;MACrC6B,SAAS,CAACC,SAAS,CAACC,SAAS,CAAClC,gBAAgB,CAACG,gBAAgB,CAAC;MAChEJ,UAAU,CAAC,gCAAgC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMoC,WAAW,GAAGA,CAAA,KAAM;IACxBtC,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IACdX,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEJ,OAAA,CAAC3B,MAAM;IACL6B,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAE+C,WAAY;IACrBC,QAAQ,EAAC,IAAI;IACbC,SAAS;IAAAC,QAAA,gBAETrD,OAAA,CAAC1B,WAAW;MAAA+E,QAAA,eACVrD,OAAA,CAACrB,GAAG;QAAC2E,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzDrD,OAAA,CAACR,QAAQ;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACZ7D,OAAA,CAACpB,UAAU;UAACkF,OAAO,EAAC,IAAI;UAAAT,QAAA,GAAC,sBACH,EAACjD,QAAQ,CAAC2D,IAAI;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd7D,OAAA,CAACzB,aAAa;MAAA8E,QAAA,gBACZrD,OAAA,CAACrB,GAAG;QAAC2E,EAAE,EAAE;UAAEU,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,eAC1DrD,OAAA,CAAChB,IAAI;UAACmF,KAAK,EAAE5D,SAAU;UAAC6D,QAAQ,EAAExC,eAAgB;UAAAyB,QAAA,gBAChDrD,OAAA,CAACf,GAAG;YACFoF,IAAI,eAAErE,OAAA,CAACZ,cAAc;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBS,KAAK,EAAC,qBAAqB;YAC3BC,YAAY,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF7D,OAAA,CAACf,GAAG;YACFoF,IAAI,eAAErE,OAAA,CAACN,OAAO;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAClBS,KAAK,EAAC,iBAAiB;YACvBC,YAAY,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAELlD,KAAK,iBACJX,OAAA,CAACnB,KAAK;QAAC2F,QAAQ,EAAC,OAAO;QAAClB,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,EACnC1C;MAAK;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAEAhD,OAAO,iBACNb,OAAA,CAACnB,KAAK;QAAC2F,QAAQ,EAAC,SAAS;QAAClB,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,EACrCxC;MAAO;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EAGAtD,SAAS,KAAK,CAAC,iBACdP,OAAA,CAACrB,GAAG;QAAA0E,QAAA,gBACFrD,OAAA,CAACpB,UAAU;UAACkF,OAAO,EAAC,OAAO;UAACW,KAAK,EAAC,gBAAgB;UAACnB,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACtB,SAAS;UACR0E,SAAS;UACTkB,KAAK,EAAC,kBAAkB;UACxBI,IAAI,EAAE3D,gBAAgB,CAACI,YAAY,GAAG,MAAM,GAAG,UAAW;UAC1DgD,KAAK,EAAEpD,gBAAgB,CAACE,eAAgB;UACxCmD,QAAQ,EAAGO,CAAC,IAAK3D,mBAAmB,CAACqB,IAAI,KAAK;YAC5C,GAAGA,IAAI;YACPpB,eAAe,EAAE0D,CAAC,CAACC,MAAM,CAACT;UAC5B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACV9E,OAAA,CAACjB,cAAc;cAACgG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BrD,OAAA,CAAClB,UAAU;gBACTkG,OAAO,EAAEA,CAAA,KAAMhE,mBAAmB,CAACqB,IAAI,KAAK;kBAC1C,GAAGA,IAAI;kBACPlB,YAAY,EAAE,CAACkB,IAAI,CAAClB;gBACtB,CAAC,CAAC,CAAE;gBACJ8D,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETtC,gBAAgB,CAACI,YAAY,gBAAGnB,OAAA,CAACV,iBAAiB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACZ,cAAc;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAED9C,gBAAgB,CAACG,gBAAgB,iBAChClB,OAAA,CAACrB,GAAG;UAAC2E,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,eAAe;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBAClErD,OAAA,CAACpB,UAAU;YAACkF,OAAO,EAAC,WAAW;YAACwB,YAAY;YAAAjC,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7D,OAAA,CAACrB,GAAG;YAAC2E,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACzDrD,OAAA,CAACpB,UAAU;cACTkF,OAAO,EAAC,IAAI;cACZR,EAAE,EAAE;gBACFiC,UAAU,EAAE,WAAW;gBACvBH,OAAO,EAAE,kBAAkB;gBAC3BD,CAAC,EAAE,CAAC;gBACJE,YAAY,EAAE,CAAC;gBACfG,IAAI,EAAE;cACR,CAAE;cAAAnC,QAAA,EAEDtC,gBAAgB,CAACG;YAAgB;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACb7D,OAAA,CAAClB,UAAU;cACTkG,OAAO,EAAElC,kBAAmB;cAC5B2C,KAAK,EAAC,gBAAgB;cAAApC,QAAA,eAEtBrD,OAAA,CAACJ,eAAe;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED7D,OAAA,CAACrB,GAAG;UAAC2E,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAE3B,OAAO,EAAE,MAAM;YAAEmC,cAAc,EAAE;UAAS,CAAE;UAAArC,QAAA,eAC5DrD,OAAA,CAACvB,MAAM;YACLqF,OAAO,EAAC,WAAW;YACnBkB,OAAO,EAAEjD,kBAAmB;YAC5B4D,QAAQ,EAAElF,OAAO,IAAI,CAACM,gBAAgB,CAACE,eAAe,CAACe,IAAI,CAAC,CAAE;YAC9D4D,SAAS,eAAE5F,OAAA,CAACZ,cAAc;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EAE7B5C,OAAO,GAAG,aAAa,GAAG;UAAqB;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAtD,SAAS,KAAK,CAAC,iBACdP,OAAA,CAACrB,GAAG;QAAA0E,QAAA,gBACFrD,OAAA,CAACpB,UAAU;UAACkF,OAAO,EAAC,OAAO;UAACW,KAAK,EAAC,gBAAgB;UAACnB,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAACtB,SAAS;UACR0E,SAAS;UACTkB,KAAK,EAAC,kBAAkB;UACxBI,IAAI,EAAEtD,kBAAkB,CAACI,mBAAmB,GAAG,MAAM,GAAG,UAAW;UACnE2C,KAAK,EAAE/C,kBAAkB,CAACH,eAAgB;UAC1CmD,QAAQ,EAAGO,CAAC,IAAKtD,qBAAqB,CAACgB,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPpB,eAAe,EAAE0D,CAAC,CAACC,MAAM,CAACT;UAC5B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACV9E,OAAA,CAACjB,cAAc;cAACgG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BrD,OAAA,CAAClB,UAAU;gBACTkG,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAACgB,IAAI,KAAK;kBAC5C,GAAGA,IAAI;kBACPb,mBAAmB,EAAE,CAACa,IAAI,CAACb;gBAC7B,CAAC,CAAC,CAAE;gBACJyD,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETjC,kBAAkB,CAACI,mBAAmB,gBAAGxB,OAAA,CAACV,iBAAiB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACZ,cAAc;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEF7D,OAAA,CAACd,OAAO;UAACoE,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1B7D,OAAA,CAACtB,SAAS;UACR0E,SAAS;UACTkB,KAAK,EAAC,gBAAgB;UACtBI,IAAI,EAAEtD,kBAAkB,CAACK,eAAe,GAAG,MAAM,GAAG,UAAW;UAC/D0C,KAAK,EAAE/C,kBAAkB,CAACE,aAAc;UACxC8C,QAAQ,EAAGO,CAAC,IAAKtD,qBAAqB,CAACgB,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPf,aAAa,EAAEqD,CAAC,CAACC,MAAM,CAACT;UAC1B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACV9E,OAAA,CAACjB,cAAc;cAACgG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BrD,OAAA,CAAClB,UAAU;gBACTkG,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAACgB,IAAI,KAAK;kBAC5C,GAAGA,IAAI;kBACPZ,eAAe,EAAE,CAACY,IAAI,CAACZ;gBACzB,CAAC,CAAC,CAAE;gBACJwD,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETjC,kBAAkB,CAACK,eAAe,gBAAGzB,OAAA,CAACV,iBAAiB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACZ,cAAc;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEF7D,OAAA,CAACtB,SAAS;UACR0E,SAAS;UACTkB,KAAK,EAAC,yBAAyB;UAC/BI,IAAI,EAAEtD,kBAAkB,CAACM,mBAAmB,GAAG,MAAM,GAAG,UAAW;UACnEyC,KAAK,EAAE/C,kBAAkB,CAACG,gBAAiB;UAC3C6C,QAAQ,EAAGO,CAAC,IAAKtD,qBAAqB,CAACgB,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPd,gBAAgB,EAAEoD,CAAC,CAACC,MAAM,CAACT;UAC7B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACV9E,OAAA,CAACjB,cAAc;cAACgG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BrD,OAAA,CAAClB,UAAU;gBACTkG,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAACgB,IAAI,KAAK;kBAC5C,GAAGA,IAAI;kBACPX,mBAAmB,EAAE,CAACW,IAAI,CAACX;gBAC7B,CAAC,CAAC,CAAE;gBACJuD,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETjC,kBAAkB,CAACM,mBAAmB,gBAAG1B,OAAA,CAACV,iBAAiB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACZ,cAAc;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEF7D,OAAA,CAACrB,GAAG;UAAC2E,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAE3B,OAAO,EAAE,MAAM;YAAEmC,cAAc,EAAE;UAAS,CAAE;UAAArC,QAAA,eAC5DrD,OAAA,CAACvB,MAAM;YACLqF,OAAO,EAAC,WAAW;YACnBkB,OAAO,EAAEtC,oBAAqB;YAC9BiD,QAAQ,EAAElF,OAAO,IAAI,CAACW,kBAAkB,CAACH,eAAe,CAACe,IAAI,CAAC,CAAC,IAAI,CAACZ,kBAAkB,CAACE,aAAa,CAACU,IAAI,CAAC,CAAE;YAC5G4D,SAAS,eAAE5F,OAAA,CAACN,OAAO;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EAEtB5C,OAAO,GAAG,WAAW,GAAG;UAAiB;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhB7D,OAAA,CAACxB,aAAa;MAAA6E,QAAA,eACZrD,OAAA,CAACvB,MAAM;QAACuG,OAAO,EAAE9B,WAAY;QAAAG,QAAA,EAAC;MAE9B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACvD,EAAA,CAnZIL,wBAAwB;AAAA6F,EAAA,GAAxB7F,wBAAwB;AAqZ9B,eAAeA,wBAAwB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}