import React, { useState, useEffect } from 'react';
import '../../styles/reports.css';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  PieChart as PieChartIcon,
  Timeline as TimelineIcon,
  List as ListIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Refresh as RefreshIcon,
  ArrowBack as ArrowBackIcon,
  DateRange as DateRangeIcon,
  Cable as CableIcon,
  Inventory as InventoryIcon,
  ExpandMore as ExpandMoreIcon,
  Show<PERSON>hart as ShowChartIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import reportService from '../../services/reportService';
import FilterableTable from '../../components/common/FilterableTable';
import EmptyState from '../../components/common/EmptyState';
import MetricCard from '../../components/common/MetricCard';
import ReportSection from '../../components/common/ReportSection';

// Import dei componenti grafici
import ProgressChart from '../../components/charts/ProgressChart';
import BobineChart from '../../components/charts/BobineChart';
import BoqChart from '../../components/charts/BoqChart';
import TimelineChart from '../../components/charts/TimelineChart';
import CaviStatoChart from '../../components/charts/CaviStatoChart';

const ReportCaviPageNew = () => {
  const navigate = useNavigate();
  const { cantiereId } = useParams();
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [selectedReport, setSelectedReport] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedReportType, setSelectedReportType] = useState('progress');
  const [formData, setFormData] = useState({
    formato: 'video',
    data_inizio: '',
    data_fine: '',
    id_bobina: ''
  });

  // New state to store all report data
  const [reportsData, setReportsData] = useState({
    progress: null,
    boq: null,
    bobine: null,
    caviStato: null,
    bobinaSpecifica: null,
    posaPeriodo: null
  });

  // State per controllo visualizzazione grafici
  const [showCharts, setShowCharts] = useState(true);

  // Load all basic reports on component mount
  useEffect(() => {
    const loadAllReports = async () => {
      setLoading(true);
      try {
        // Create individual promises that handle their own errors
        const progressPromise = reportService.getProgressReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading progress report:', err);
            return { content: null };
          });

        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading BOQ report:', err);
            return { content: null };
          });

        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading bobine report:', err);
            return { content: null };
          });

        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading cavi stato report:', err);
            return { content: null };
          });

        // Wait for all promises to resolve (they won't reject due to the catch handlers)
        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([
          progressPromise,
          boqPromise,
          bobinePromise,
          caviStatoPromise
        ]);

        // Set the data for each report, even if some are null
        setReportsData({
          progress: progressData.content,
          boq: boqData.content,
          bobine: bobineData.content,
          caviStato: caviStatoData.content,
          bobinaSpecifica: null,
          posaPeriodo: null
        });

        // Only set error to null if we successfully loaded at least one report
        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {
          setError(null);
        } else {
          setError('Errore nel caricamento dei report. Riprova più tardi.');
        }
      } catch (err) {
        // This catch block should rarely be hit due to the individual error handling above
        console.error('Unexpected error loading reports:', err);
        setError('Errore nel caricamento dei report. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    if (cantiereId) {
      loadAllReports();
    }
  }, [cantiereId]);

  // Configurazione dei report disponibili
  const reportTypes = [
    {
      id: 'progress',
      title: 'Report Avanzamento',
      description: 'Panoramica completa dell\'avanzamento dei lavori con metriche di performance e previsioni',
      icon: <AssessmentIcon />,
      color: 'primary',
      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']
    },
    {
      id: 'boq',
      title: 'Bill of Quantities',
      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',
      icon: <ListIcon />,
      color: 'secondary',
      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']
    },
    {
      id: 'bobine',
      title: 'Report Utilizzo Bobine',
      description: 'Analisi completa dell\'utilizzo delle bobine con efficienza e sprechi',
      icon: <InventoryIcon />,
      color: 'success',
      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']
    },
    {
      id: 'bobina-specifica',
      title: 'Report Bobina Specifica',
      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',
      icon: <CableIcon />,
      color: 'info',
      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']
    },
    {
      id: 'posa-periodo',
      title: 'Report Posa per Periodo',
      description: 'Analisi temporale della posa con trend e pattern di lavoro',
      icon: <TimelineIcon />,
      color: 'warning',
      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']
    },
    {
      id: 'cavi-stato',
      title: 'Report Cavi per Stato',
      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',
      icon: <BarChartIcon />,
      color: 'error',
      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']
    }
  ];

  // Nuova funzione per generare report con formato specificato
  const generateReportWithFormat = async (reportType, format) => {
    try {
      setLoading(true);
      setError(null);

      let response;

      switch (reportType) {
        case 'progress':
          response = await reportService.getProgressReport(cantiereId, format);
          break;
        case 'boq':
          response = await reportService.getBillOfQuantities(cantiereId, format);
          break;
        case 'bobine':
          response = await reportService.getBobineReport(cantiereId, format);
          break;
        case 'cavi-stato':
          response = await reportService.getCaviStatoReport(cantiereId, format);
          break;
        case 'bobina-specifica':
          if (!formData.id_bobina) {
            setError('Inserisci l\'ID della bobina');
            return;
          }
          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);
          break;
        case 'posa-periodo':
          if (!formData.data_inizio || !formData.data_fine) {
            setError('Seleziona le date di inizio e fine periodo');
            return;
          }
          response = await reportService.getPosaPerPeriodoReport(
            cantiereId,
            formData.data_inizio,
            formData.data_fine,
            format
          );
          break;
        default:
          throw new Error('Tipo di report non riconosciuto');
      }

      if (format === 'video') {
        // For special reports, update the specific report data
        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {
          setReportsData(prev => ({
            ...prev,
            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content
          }));
        }
        setReportData(response.content);
      } else {
        // Per PDF/Excel, apri il link di download
        if (response.file_url) {
          window.open(response.file_url, '_blank');
        }
      }
    } catch (err) {
      console.error('Errore nella generazione del report:', err);
      setError(err.detail || err.message || 'Errore durante la generazione del report');
    } finally {
      setLoading(false);
    }
  };

  const handleReportSelect = (reportType) => {
    setSelectedReport(reportType);
    setDialogType(reportType.id);

    // Per report che necessitano di parametri aggiuntivi, mostra il dialog
    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {
      // Imposta valori di default per alcuni report
      if (reportType.id === 'posa-periodo') {
        const today = new Date();
        const lastMonth = new Date();
        lastMonth.setMonth(today.getMonth() - 1);

        setFormData({
          ...formData,
          data_inizio: lastMonth.toISOString().split('T')[0],
          data_fine: today.toISOString().split('T')[0]
        });
      }

      setOpenDialog(true);
    } else {
      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'
      generateReportWithFormat(reportType.id, 'video');
    }
  };

  const handleGenerateReport = async () => {
    await generateReportWithFormat(dialogType, formData.formato);
    setOpenDialog(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
    setFormData({
      formato: 'video',
      data_inizio: '',
      data_fine: '',
      id_bobina: ''
    });
  };

  const renderReportContent = () => {
    if (!reportData) return null;

    return (
      <Paper sx={{ p: 3, mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            {selectedReport?.title} - {reportData.nome_cantiere}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Export buttons */}
            <Button
              startIcon={<DownloadIcon />}
              onClick={() => generateReportWithFormat(dialogType, 'pdf')}
              variant="outlined"
              size="small"
              color="primary"
            >
              PDF
            </Button>
            <Button
              startIcon={<DownloadIcon />}
              onClick={() => generateReportWithFormat(dialogType, 'excel')}
              variant="outlined"
              size="small"
              color="success"
            >
              Excel
            </Button>
            <Button
              startIcon={<RefreshIcon />}
              onClick={() => setReportData(null)}
              variant="outlined"
              size="small"
            >
              Nuovo Report
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Renderizza il contenuto specifico del report */}
        {dialogType === 'progress' && renderProgressReport(reportData)}
        {dialogType === 'boq' && renderBoqReport(reportData)}
        {dialogType === 'bobine' && renderBobineReport(reportData)}
        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}
        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}
        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}
      </Paper>
    );
  };

  const renderProgressReport = (data) => (
    <Box>
      {/* Header con controlli migliorato */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        p: 2,
        bgcolor: '#f8f9fa',
        borderRadius: 2,
        border: '1px solid #e0e0e0'
      }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50' }}>
          📊 Report Avanzamento Lavori
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Metriche Principali - Cards Moderne con MetricCard */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Totali"
            value={data.metri_totali}
            unit="m"
            subtitle="Lunghezza complessiva del progetto"
            gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
            size="medium"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Posati"
            value={data.metri_posati}
            unit="m"
            subtitle={`${data.percentuale_avanzamento}% completato`}
            gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
            progress={data.percentuale_avanzamento}
            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}
            trendValue={`${data.percentuale_avanzamento}%`}
            size="medium"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Metri Rimanenti"
            value={data.metri_da_posare}
            unit="m"
            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}
            gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
            size="medium"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Media/Giorno"
            value={data.media_giornaliera || 0}
            unit="m"
            subtitle={
              data.giorni_stimati
                ? `${data.giorni_stimati} giorni lavorativi rimasti`
                : (data.media_giornaliera > 0
                    ? 'Calcolo in corso'
                    : 'Nessuna posa recente')
            }
            gradient="linear-gradient(135deg, #fa709a 0%, #fee140 100%)"
            size="medium"
            tooltip={
              data.giorni_lavorativi_effettivi
                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`
                : 'Media giornaliera basata sui giorni di lavoro effettivo'
            }
          />
        </Grid>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <ProgressChart data={data} />
        </Box>
      )}

      {/* Dettagli Performance - Cards Informative */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Stato Cavi
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>
                      {data.totale_cavi}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      Cavi Totali
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>
                      {data.cavi_posati}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666' }}>
                      Cavi Posati ({data.percentuale_cavi}%)
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Progresso</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {data.percentuale_cavi}%
                  </Typography>
                </Box>
                <Box sx={{
                  width: '100%',
                  height: 8,
                  bgcolor: '#e0e0e0',
                  borderRadius: 4,
                  overflow: 'hidden'
                }}>
                  <Box sx={{
                    width: `${data.percentuale_cavi}%`,
                    height: '100%',
                    bgcolor: '#27ae60',
                    transition: 'width 0.3s ease'
                  }} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TimelineIcon sx={{ color: '#e74c3c', mr: 1, fontSize: 28 }} />
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Timeline Progetto
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#e74c3c', mb: 1 }}>
                  {data.media_giornaliera || 0}m
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                  Media Giornaliera
                </Typography>
                {data.giorni_lavorativi_effettivi && (
                  <Typography variant="caption" sx={{ color: '#999', fontSize: '0.75rem' }}>
                    Basata su {data.giorni_lavorativi_effettivi} giorni di lavoro effettivo
                  </Typography>
                )}
              </Box>
              {data.giorni_stimati ? (
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, color: '#856404', mb: 0.5 }}>
                    {data.data_completamento}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#856404' }}>
                    Completamento previsto in {data.giorni_stimati} giorni
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    {data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Attività Recente - Design Migliorato */}
      {data.posa_recente && data.posa_recente.length > 0 && (
        <Card sx={{ border: '1px solid #e0e0e0' }}>
          <CardContent sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                📈 Attività Recente
              </Typography>
            </Box>

            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}
            <Grid container spacing={2}>
              {data.posa_recente.slice(0, 5).map((posa, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Box sx={{
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 2,
                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',
                    transition: 'all 0.2s',
                    '&:hover': {
                      bgcolor: '#f5f5f5',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                    }
                  }}>
                    <Typography variant="body2" sx={{ color: '#666', mb: 1 }}>
                      {posa.data}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#2c3e50' }}>
                      {posa.metri}m
                    </Typography>
                    {index === 0 && (
                      <Chip
                        label="Più recente"
                        size="small"
                        sx={{
                          mt: 1,
                          bgcolor: '#3498db',
                          color: 'white',
                          fontSize: '0.7rem'
                        }}
                      />
                    )}
                  </Box>
                </Grid>
              ))}
            </Grid>

            {/* Link per vedere tutti i dati se ce ne sono di più */}
            {data.posa_recente.length > 5 && (
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="body2" sx={{ color: '#3498db' }}>
                      Mostra tutti i {data.posa_recente.length} record
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <FilterableTable
                      data={data.posa_recente.map(posa => ({
                        data: posa.data,
                        metri: `${posa.metri}m`
                      }))}
                      columns={[
                        { field: 'data', headerName: 'Data', width: 200 },
                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }
                      ]}
                      pagination={true}
                      pageSize={10}
                    />
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
          </CardContent>
        </Card>
      )}
    </Box>
  );

  const renderBoqReport = (data) => (
    <Box>
      {/* Header migliorato */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 3,
        p: 2,
        bgcolor: '#f8f9fa',
        borderRadius: 2,
        border: '1px solid #e0e0e0'
      }}>
        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />
        <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50' }}>
          📋 Bill of Quantities - Distinta Materiali
        </Typography>
      </Box>

      {/* Grafici BOQ se disponibili */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <BoqChart data={data} />
        </Box>
      )}

      {/* Cavi per Tipologia - Design migliorato */}
      <Card sx={{ mb: 3, border: '1px solid #e0e0e0' }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <CableIcon sx={{ color: '#e67e22', mr: 1, fontSize: 24 }} />
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
              Cavi per Tipologia
            </Typography>
          </Box>
          <FilterableTable
            data={data.cavi_per_tipo || []}
            columns={[
              { field: 'tipologia', headerName: 'Tipologia', width: 150 },
              { field: 'sezione', headerName: 'Sezione', width: 100 },
              { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },
              { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',
                renderCell: (row) => `${row.metri_teorici}m` },
              { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',
                renderCell: (row) => `${row.metri_reali}m` },
              { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',
                renderCell: (row) => `${row.metri_da_posare}m` }
            ]}
            pageSize={10}
          />
        </CardContent>
      </Card>

      {/* Bobine Disponibili - Design migliorato */}
      <Card sx={{ border: '1px solid #e0e0e0' }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <InventoryIcon sx={{ color: '#16a085', mr: 1, fontSize: 24 }} />
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
              Bobine Disponibili
            </Typography>
          </Box>
          <FilterableTable
            data={data.bobine_per_tipo || []}
            columns={[
              { field: 'tipologia', headerName: 'Tipologia', width: 150 },
              { field: 'sezione', headerName: 'Sezione', width: 100 },
              { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },
              { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',
                renderCell: (row) => `${row.metri_disponibili}m` }
            ]}
            pageSize={10}
          />
        </CardContent>
      </Card>
    </Box>
  );

  const renderBobineReport = (data) => (
    <Box>
      {/* Header con controlli */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 3 }}>
          <BobineChart data={data} />
        </Box>
      )}

      {/* Bobine del Cantiere */}
      <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>
          Utilizzo Bobine del Cantiere
        </Typography>
        <FilterableTable
          data={data.bobine || []}
          columns={[
            { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },
            { field: 'tipologia', headerName: 'Tipologia', width: 150 },
            { field: 'sezione', headerName: 'Sezione', width: 100 },
            { field: 'stato', headerName: 'Stato', width: 120,
              renderCell: (row) => (
                <Chip
                  label={row.stato}
                  color={row.stato === 'DISPONIBILE' ? 'primary' : 'default'}
                  size="small"
                  sx={{
                    bgcolor: row.stato === 'DISPONIBILE' ? '#3498db' : '#85929e',
                    color: 'white'
                  }}
                />
              )
            },
            { field: 'metri_totali', headerName: 'Metri Totali', width: 120, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_totali}m` },
            { field: 'metri_residui', headerName: 'Metri Residui', width: 120, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_residui}m` },
            { field: 'metri_utilizzati', headerName: 'Metri Utilizzati', width: 140, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_utilizzati}m` },
            { field: 'percentuale_utilizzo', headerName: 'Utilizzo', width: 100, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.percentuale_utilizzo}%` }
          ]}
          pageSize={10}
        />
      </Paper>
    </Box>
  );

  const renderBobinaSpecificaReport = (data) => (
    <Box>
      {/* Header */}
      <Typography variant="h5" sx={{ fontWeight: 600, color: 'info.main', mb: 3 }}>
        Report Bobina Specifica - {data.bobina?.id_bobina}
      </Typography>

      <Grid container spacing={3}>
        {/* Dettagli Bobina */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Dettagli Bobina
            </Typography>
            {data.bobina && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">ID Bobina:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.id_bobina}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Tipologia:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.tipologia}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Sezione:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.sezione}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Stato:</Typography>
                  <Chip
                    label={data.bobina.stato}
                    color={data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Metriche Utilizzo */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Metriche Utilizzo
            </Typography>
            {data.bobina && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Metri Totali:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.metri_totali}m</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Metri Utilizzati:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600, color: 'success.main' }}>
                    {data.bobina.metri_utilizzati}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Metri Residui:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600, color: 'warning.main' }}>
                    {data.bobina.metri_residui}m
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body1">Percentuale Utilizzo:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>{data.bobina.percentuale_utilizzo}%</Typography>
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Cavi Associati */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Cavi Associati ({data.totale_cavi})
            </Typography>
            <FilterableTable
              data={data.cavi_associati || []}
              columns={[
                { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },
                { field: 'sistema', headerName: 'Sistema', width: 120 },
                { field: 'utility', headerName: 'Utility', width: 120 },
                { field: 'tipologia', headerName: 'Tipologia', width: 150 },
                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',
                  renderCell: (row) => `${row.metri_teorici}m` },
                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',
                  renderCell: (row) => `${row.metri_reali}m` },
                { field: 'stato', headerName: 'Stato', width: 120,
                  renderCell: (row) => (
                    <Chip
                      label={row.stato}
                      color={row.stato === 'POSATO' ? 'success' : 'warning'}
                      size="small"
                    />
                  )
                }
              ]}
              pageSize={10}
            />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );

  const renderPosaPeriodoReport = (data) => (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'warning.main' }}>
          Report Posa per Periodo
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Statistiche Periodo - Layout Orizzontale */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.totale_metri_periodo}m
            </Typography>
            <Typography variant="body1">Metri Totali</Typography>
            <Typography variant="caption">{data.data_inizio} - {data.data_fine}</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.giorni_attivi}
            </Typography>
            <Typography variant="body1">Giorni Attivi</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {data.media_giornaliera}m
            </Typography>
            <Typography variant="body1">Media/Giorno</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m
            </Typography>
            <Typography variant="body1">Media/Settimana</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Box sx={{ mb: 4 }}>
          <TimelineChart data={data} />
        </Box>
      )}

      {/* Posa Giornaliera */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Dettaglio Posa Giornaliera
        </Typography>
        <FilterableTable
          data={data.posa_giornaliera || []}
          columns={[
            { field: 'data', headerName: 'Data', width: 200 },
            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri}m` }
          ]}
          pageSize={10}
        />
      </Paper>
    </Box>
  );

  const renderCaviStatoReport = (data) => (
    <Box>
      {/* Header con controlli */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={showCharts}
              onChange={(e) => setShowCharts(e.target.checked)}
              color="primary"
            />
          }
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ShowChartIcon sx={{ mr: 1 }} />
              Grafici
            </Box>
          }
        />
      </Box>

      {/* Grafici migliorati */}
      {showCharts && (
        <Paper sx={{ p: 2, mb: 3, border: '1px solid #e0e0e0' }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>
            Distribuzione Stati Cavi
          </Typography>
          <Box sx={{
            border: '1px solid #e0e0e0',
            borderRadius: 1,
            overflow: 'hidden'
          }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f8f9fa' }}>
                  <th style={{
                    padding: '8px 12px',
                    textAlign: 'left',
                    fontSize: '12px',
                    fontWeight: 600,
                    color: '#2c3e50',
                    borderBottom: '1px solid #e0e0e0',
                    width: '25%'
                  }}>Stato</th>
                  <th style={{
                    padding: '8px 12px',
                    textAlign: 'center',
                    fontSize: '12px',
                    fontWeight: 600,
                    color: '#2c3e50',
                    borderBottom: '1px solid #e0e0e0',
                    width: '25%'
                  }}>Numero Cavi</th>
                  <th style={{
                    padding: '8px 12px',
                    textAlign: 'center',
                    fontSize: '12px',
                    fontWeight: 600,
                    color: '#2c3e50',
                    borderBottom: '1px solid #e0e0e0',
                    width: '25%'
                  }}>Metri Teorici</th>
                  <th style={{
                    padding: '8px 12px',
                    textAlign: 'center',
                    fontSize: '12px',
                    fontWeight: 600,
                    color: '#2c3e50',
                    borderBottom: '1px solid #e0e0e0',
                    width: '25%'
                  }}>Distribuzione</th>
                </tr>
              </thead>
              <tbody>
                {(data.cavi_per_stato || []).map((item, index) => {
                  const totalCavi = (data.cavi_per_stato || []).reduce((sum, s) => sum + s.num_cavi, 0);
                  const percentage = totalCavi > 0 ? Math.round((item.num_cavi / totalCavi) * 100) : 0;
                  return (
                    <tr key={index}>
                      <td style={{
                        padding: '12px',
                        fontSize: '13px',
                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'
                      }}>
                        <Chip
                          label={item.stato}
                          size="small"
                          sx={{
                            bgcolor: item.stato === 'Installato' ? '#3498db' : '#85929e',
                            color: 'white'
                          }}
                        />
                      </td>
                      <td style={{
                        padding: '12px',
                        fontSize: '13px',
                        textAlign: 'center',
                        fontWeight: 600,
                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'
                      }}>{item.num_cavi}</td>
                      <td style={{
                        padding: '12px',
                        fontSize: '13px',
                        textAlign: 'center',
                        fontWeight: 600,
                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'
                      }}>{item.metri_teorici}m</td>
                      <td style={{
                        padding: '12px',
                        fontSize: '13px',
                        textAlign: 'center',
                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                          <Box sx={{
                            width: '60px',
                            height: '6px',
                            bgcolor: '#e0e0e0',
                            borderRadius: '3px',
                            position: 'relative'
                          }}>
                            <Box sx={{
                              width: `${percentage}%`,
                              height: '100%',
                              bgcolor: item.stato === 'Installato' ? '#3498db' : '#85929e',
                              borderRadius: '3px'
                            }} />
                          </Box>
                          <Typography variant="caption" sx={{ fontWeight: 600 }}>
                            {percentage}%
                          </Typography>
                        </Box>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </Box>
        </Paper>
      )}

      {/* Tabella Dettagliata */}
      <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>
          Cavi per Stato di Installazione
        </Typography>
        <FilterableTable
          data={data.cavi_per_stato || []}
          columns={[
            { field: 'stato', headerName: 'Stato', width: 150,
              renderCell: (row) => (
                <Chip
                  label={row.stato}
                  size="small"
                  sx={{
                    bgcolor: row.stato === 'Installato' ? '#3498db' : '#85929e',
                    color: 'white'
                  }}
                />
              )
            },
            { field: 'num_cavi', headerName: 'Numero Cavi', width: 120, align: 'right', dataType: 'number' },
            { field: 'metri_teorici', headerName: 'Metri Teorici', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_teorici}m` },
            { field: 'metri_reali', headerName: 'Metri Reali', width: 150, align: 'right', dataType: 'number',
              renderCell: (row) => `${row.metri_reali}m` }
          ]}
          pagination={false}
        />
      </Paper>
    </Box>
  );

  const renderDialog = () => (
    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
      <DialogTitle>
        {selectedReport?.title}
      </DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Formato</InputLabel>
              <Select
                value={formData.formato}
                label="Formato"
                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}
              >
                <MenuItem value="video">Visualizza a schermo</MenuItem>
                <MenuItem value="pdf">Download PDF</MenuItem>
                <MenuItem value="excel">Download Excel</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {dialogType === 'bobina-specifica' && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="ID Bobina"
                value={formData.id_bobina}
                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}
                placeholder="Es: 1, 2, A, B..."
                helperText="Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)"
              />
            </Grid>
          )}

          {dialogType === 'posa-periodo' && (
            <>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Inizio"
                  value={formData.data_inizio}
                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Fine"
                  value={formData.data_fine}
                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseDialog}>Annulla</Button>
        <Button
          onClick={handleGenerateReport}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}
        >
          {loading ? 'Generazione...' : 'Genera Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box className="report-main-container report-fade-in">
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton
            onClick={() => navigate(-1)}
            color="primary"
            sx={{
              bgcolor: '#f8f9fa',
              '&:hover': { bgcolor: '#e9ecef' }
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" className="report-title" sx={{ fontWeight: 700, color: '#2c3e50' }}>
            📊 Dashboard Report
          </Typography>
        </Box>
        <AdminHomeButton />
      </Box>

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Reports Navigation */}
      <Box sx={{ mt: 3 }}>
        {/* Report Navigation - Design Cards Moderne */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" sx={{ fontWeight: 600, color: '#2c3e50', mb: 3, textAlign: 'center' }}>
            🎯 Seleziona il tipo di report
          </Typography>
          <Grid container spacing={3}>
            {/* Report Avanzamento */}
            <Grid item xs={12} sm={6} md={4}>
              <Card
                className={`report-card report-scale-in ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white'
                }}
                onClick={() => setSelectedReportType('progress')}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <AssessmentIcon sx={{ fontSize: 48, color: '#3498db', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Report Avanzamento
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                    Panoramica completa dell'avanzamento dei lavori
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                    <Chip label="Metri posati" size="small" />
                    <Chip label="Performance" size="small" />
                    <Chip label="Timeline" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Bill of Quantities */}
            <Grid item xs={12} sm={6} md={4}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
                onClick={() => setSelectedReportType('boq')}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <ListIcon sx={{ fontSize: 48, color: '#8e44ad', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Bill of Quantities
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                    Distinta materiali dettagliata
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                    <Chip label="Materiali" size="small" />
                    <Chip label="Consumi" size="small" />
                    <Chip label="Disponibilità" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Report Bobine */}
            <Grid item xs={12} sm={6} md={4}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  border: selectedReportType === 'bobine' ? '2px solid #16a085' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'bobine' ? '#f0fff4' : 'white',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
                onClick={() => setSelectedReportType('bobine')}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <InventoryIcon sx={{ fontSize: 48, color: '#16a085', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Report Bobine
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                    Analisi utilizzo bobine
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                    <Chip label="Utilizzo" size="small" />
                    <Chip label="Efficienza" size="small" />
                    <Chip label="Sprechi" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Cavi per Stato */}
            <Grid item xs={12} sm={6} md={4}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  border: selectedReportType === 'cavi-stato' ? '2px solid #e74c3c' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'cavi-stato' ? '#fff5f5' : 'white',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
                onClick={() => setSelectedReportType('cavi-stato')}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <BarChartIcon sx={{ fontSize: 48, color: '#e74c3c', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Cavi per Stato
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                    Classificazione per stato installazione
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                    <Chip label="Stati" size="small" />
                    <Chip label="Statistiche" size="small" />
                    <Chip label="Problemi" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Bobina Specifica */}
            <Grid item xs={12} sm={6} md={4}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  border: selectedReportType === 'bobina-specifica' ? '2px solid #f39c12' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'bobina-specifica' ? '#fffbf0' : 'white',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
                onClick={() => setSelectedReportType('bobina-specifica')}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <CableIcon sx={{ fontSize: 48, color: '#f39c12', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Bobina Specifica
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                    Dettaglio singola bobina
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                    <Chip label="Dettaglio" size="small" />
                    <Chip label="Cavi" size="small" />
                    <Chip label="Storico" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Posa per Periodo */}
            <Grid item xs={12} sm={6} md={4}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',
                  bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
                onClick={() => setSelectedReportType('posa-periodo')}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <TimelineIcon sx={{ fontSize: 48, color: '#9b59b6', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Posa per Periodo
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                    Analisi temporale della posa
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                    <Chip label="Trend" size="small" />
                    <Chip label="Performance" size="small" />
                    <Chip label="Produttività" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Report Content */}
        <Box sx={{ minHeight: '400px' }}>
          {/* Progress Report */}
          {selectedReportType === 'progress' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.progress ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderProgressReport(reportsData.progress)}
                </Box>
              ) : loading ? (
                <EmptyState
                  type="loading"
                  reportType="progress"
                  title="Caricamento Report Avanzamento..."
                  description="Stiamo elaborando i dati dell'avanzamento dei lavori"
                />
              ) : (
                <EmptyState
                  type="error"
                  reportType="progress"
                  title="Errore nel caricamento"
                  description="Impossibile caricare il report di avanzamento. Verifica la connessione e riprova."
                  onRetry={() => {
                    setLoading(true);
                    reportService.getProgressReport(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          progress: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying progress report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                  loading={loading}
                />
              )}
            </Paper>
          )}

          {/* Bill of Quantities */}
          {selectedReportType === 'boq' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.boq ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('boq', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBoqReport(reportsData.boq)}
                </Box>
              ) : loading ? (
                <EmptyState
                  type="loading"
                  reportType="boq"
                  title="Caricamento Bill of Quantities..."
                  description="Stiamo elaborando la distinta materiali"
                />
              ) : (
                <EmptyState
                  type="error"
                  reportType="boq"
                  title="Errore nel caricamento"
                  description="Impossibile caricare la distinta materiali. Verifica la connessione e riprova."
                  onRetry={() => {
                    setLoading(true);
                    reportService.getBillOfQuantities(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          boq: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying BOQ report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                  loading={loading}
                />
              )}
            </Paper>
          )}

          {/* Bobine Report */}
          {selectedReportType === 'bobine' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.bobine ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobine', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobine', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBobineReport(reportsData.bobine)}
                </Box>
              ) : loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>
                  <CircularProgress size={24} />
                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    Impossibile caricare il report. Riprova più tardi.
                  </Alert>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                      setLoading(true);
                      reportService.getBobineReport(cantiereId, 'video')
                        .then(data => {
                          setReportsData(prev => ({
                            ...prev,
                            bobine: data.content
                          }));
                        })
                        .catch(err => {
                          console.error('Error retrying bobine report:', err);
                        })
                        .finally(() => {
                          setLoading(false);
                        });
                    }}
                  >
                    Riprova
                  </Button>
                </Box>
              )}
            </Paper>
          )}

          {/* Cavi Stato Report */}
          {selectedReportType === 'cavi-stato' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.caviStato ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('cavi-stato', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderCaviStatoReport(reportsData.caviStato)}
                </Box>
              ) : loading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>
                  <CircularProgress size={24} />
                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', my: 4 }}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    Impossibile caricare il report. Riprova più tardi.
                  </Alert>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                      setLoading(true);
                      reportService.getCaviStatoReport(cantiereId, 'video')
                        .then(data => {
                          setReportsData(prev => ({
                            ...prev,
                            caviStato: data.content
                          }));
                        })
                        .catch(err => {
                          console.error('Error retrying cavi stato report:', err);
                        })
                        .finally(() => {
                          setLoading(false);
                        });
                    }}
                  >
                    Riprova
                  </Button>
                </Box>
              )}
            </Paper>
          )}

          {/* Bobina Specifica Report */}
          {selectedReportType === 'bobina-specifica' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.bobinaSpecifica ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}
                </Box>
              ) : (
                <EmptyState
                  type="action-required"
                  reportType="bobina-specifica"
                  title="Seleziona una Bobina"
                  description="Inserisci l'ID di una bobina per generare il report dettagliato con tutti i cavi associati e le metriche di utilizzo."
                  actionLabel="Seleziona Bobina"
                  onAction={() => {
                    setDialogType('bobina-specifica');
                    setOpenDialog(true);
                  }}
                />
              )}
            </Paper>
          )}

          {/* Posa per Periodo Report */}
          {selectedReportType === 'posa-periodo' && (
            <Paper sx={{ p: 3 }}>
              {reportsData.posaPeriodo ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}
                </Box>
              ) : (
                <EmptyState
                  type="action-required"
                  reportType="posa-periodo"
                  title="Seleziona un Periodo"
                  description="Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team."
                  actionLabel="Seleziona Periodo"
                  onAction={() => {
                    setDialogType('posa-periodo');
                    // Set default date range (last month to today)
                    const today = new Date();
                    const lastMonth = new Date();
                    lastMonth.setMonth(today.getMonth() - 1);

                    setFormData({
                      ...formData,
                      data_inizio: lastMonth.toISOString().split('T')[0],
                      data_fine: today.toISOString().split('T')[0]
                    });
                    setOpenDialog(true);
                  }}
                />
              )}
            </Paper>
          )}
        </Box>
      </Box>

      {/* Dialog per configurazione report */}
      {renderDialog()}
    </Box>
  );
};

export default ReportCaviPageNew;
