#!/usr/bin/env python3
"""
Script per testare la salute completa del sistema CMS.
Verifica backend, frontend, database e connessioni.
"""

import sys
import os
import time
import requests
import subprocess
import socket
from pathlib import Path

def is_port_in_use(port):
    """Verifica se una porta è in uso."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def test_backend_health(port=8001, timeout=5):
    """Testa la salute del backend."""
    print(f"\n=== Test Backend (porta {port}) ===")
    
    if not is_port_in_use(port):
        print(f"❌ Backend non in esecuzione sulla porta {port}")
        return False
    
    try:
        # Test endpoint di health
        response = requests.get(f"http://localhost:{port}/api/health", timeout=timeout)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend risponde correttamente")
            print(f"Status: {data.get('status', 'unknown')}")
            print(f"Database: {data.get('database', 'unknown')}")
            
            if data.get('database') == 'connected':
                print("✅ Database connesso")
                return True
            else:
                print("❌ Problemi con il database")
                return False
        else:
            print(f"❌ Backend risponde con status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Impossibile connettersi al backend sulla porta {port}")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Timeout nella connessione al backend")
        return False
    except Exception as e:
        print(f"❌ Errore durante il test del backend: {e}")
        return False

def test_frontend_health(port=3000, timeout=5):
    """Testa la salute del frontend."""
    print(f"\n=== Test Frontend (porta {port}) ===")
    
    if not is_port_in_use(port):
        print(f"❌ Frontend non in esecuzione sulla porta {port}")
        return False
    
    try:
        response = requests.get(f"http://localhost:{port}", timeout=timeout)
        
        if response.status_code == 200:
            print("✅ Frontend risponde correttamente")
            return True
        else:
            print(f"❌ Frontend risponde con status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Impossibile connettersi al frontend sulla porta {port}")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Timeout nella connessione al frontend")
        return False
    except Exception as e:
        print(f"❌ Errore durante il test del frontend: {e}")
        return False

def test_api_endpoints(port=8001):
    """Testa alcuni endpoint API principali."""
    print(f"\n=== Test Endpoints API ===")
    
    base_url = f"http://localhost:{port}/api"
    endpoints = [
        "/health",
        "/",
        "/docs"  # Swagger documentation
    ]
    
    results = {}
    
    for endpoint in endpoints:
        try:
            url = base_url + endpoint if endpoint != "/docs" else f"http://localhost:{port}/docs"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {endpoint}: OK")
                results[endpoint] = True
            else:
                print(f"❌ {endpoint}: Status {response.status_code}")
                results[endpoint] = False
                
        except Exception as e:
            print(f"❌ {endpoint}: Errore - {e}")
            results[endpoint] = False
    
    return all(results.values())

def check_configuration():
    """Verifica la configurazione del sistema."""
    print(f"\n=== Verifica Configurazione ===")
    
    # Verifica file di configurazione backend
    backend_config = Path("backend/config.py")
    if backend_config.exists():
        print("✅ File di configurazione backend trovato")
    else:
        print("❌ File di configurazione backend mancante")
        return False
    
    # Verifica file di configurazione frontend
    frontend_config = Path("frontend/src/config.js")
    if frontend_config.exists():
        print("✅ File di configurazione frontend trovato")
        
        # Verifica che la porta sia corretta
        with open(frontend_config, 'r') as f:
            content = f.read()
            if "localhost:8001" in content:
                print("✅ Configurazione porta frontend corretta")
            else:
                print("⚠️  Configurazione porta frontend potrebbe essere incorretta")
    else:
        print("❌ File di configurazione frontend mancante")
        return False
    
    return True

def run_database_test():
    """Esegue il test del database."""
    print(f"\n=== Test Database ===")
    
    try:
        # Esegui lo script di test del database
        result = subprocess.run([
            sys.executable, "test_database_connection.py"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Test database completato con successo")
            return True
        else:
            print("❌ Test database fallito")
            print("Output:", result.stdout)
            print("Errori:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout durante il test del database")
        return False
    except Exception as e:
        print(f"❌ Errore durante l'esecuzione del test database: {e}")
        return False

def main():
    """Funzione principale."""
    print("🏥 Test di salute del sistema CMS")
    print("=" * 50)
    
    # Cambia nella directory webapp se necessario
    if Path("webapp").exists():
        os.chdir("webapp")
        print("📁 Spostato nella directory webapp")
    
    results = {}
    
    # Test 1: Configurazione
    results['config'] = check_configuration()
    
    # Test 2: Database
    results['database'] = run_database_test()
    
    # Test 3: Backend
    results['backend'] = test_backend_health()
    
    # Test 4: Frontend
    results['frontend'] = test_frontend_health()
    
    # Test 5: API Endpoints
    if results['backend']:
        results['api'] = test_api_endpoints()
    else:
        results['api'] = False
        print("⏭️  Saltato test API (backend non disponibile)")
    
    # Riepilogo
    print(f"\n📊 Riepilogo Test")
    print("=" * 30)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.capitalize()}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"\nTest passati: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 Tutti i test sono passati! Il sistema è operativo.")
        return True
    else:
        print("⚠️  Alcuni test sono falliti. Verifica i problemi sopra riportati.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
