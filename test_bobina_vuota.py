#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per verificare che l'inserimento di metri posati con BOBINA_VUOTA funzioni correttamente.
"""

import requests
import json
import sys
import time

# Configurazione
BASE_URL = "http://localhost:8001"
CANTIERE_ID = 1
CAVO_ID = "C001"  # Assumiamo che esista un cavo con questo ID

def test_login():
    """Test di login per ottenere il token di autenticazione."""
    login_data = {
        "username": "admin",  # Utente admin del sistema
        "password": "admin"   # Password dell'utente admin
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", data=login_data)
        if response.status_code == 200:
            token = response.json().get("access_token")
            print(f"✅ Login riuscito, token ottenuto")
            return token
        else:
            print(f"❌ Login fallito: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Errore durante il login: {str(e)}")
        return None

def test_metri_posati_bobina_vuota(token):
    """Test per inserire metri posati con BOBINA_VUOTA."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Dati per l'aggiornamento dei metri posati
    metri_data = {
        "metri_posati": 300.0,
        "id_bobina": "BOBINA_VUOTA",
        "force_over": True
    }
    
    try:
        print(f"🔄 Test inserimento metri posati con BOBINA_VUOTA...")
        print(f"   Cantiere ID: {CANTIERE_ID}")
        print(f"   Cavo ID: {CAVO_ID}")
        print(f"   Metri posati: {metri_data['metri_posati']}")
        print(f"   ID Bobina: {metri_data['id_bobina']}")
        
        response = requests.post(
            f"{BASE_URL}/api/cavi/{CANTIERE_ID}/{CAVO_ID}/metri-posati",
            headers=headers,
            json=metri_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Metri posati aggiornati con successo!")
            print(f"   Stato installazione: {result.get('stato_installazione')}")
            print(f"   Metratura reale: {result.get('metratura_reale')}")
            print(f"   ID Bobina: {result.get('id_bobina')}")
            return True
        else:
            print(f"❌ Errore durante l'aggiornamento: {response.status_code}")
            print(f"   Dettaglio: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        return False

def test_verifica_cavo(token):
    """Verifica lo stato del cavo dopo l'aggiornamento."""
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/cavi/{CANTIERE_ID}/{CAVO_ID}",
            headers=headers
        )
        
        if response.status_code == 200:
            cavo = response.json()
            print(f"✅ Stato cavo verificato:")
            print(f"   ID Cavo: {cavo.get('id_cavo')}")
            print(f"   Stato installazione: {cavo.get('stato_installazione')}")
            print(f"   Metratura reale: {cavo.get('metratura_reale')}")
            print(f"   ID Bobina: {cavo.get('id_bobina')}")
            return True
        else:
            print(f"❌ Errore durante la verifica: {response.status_code}")
            print(f"   Dettaglio: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante la verifica: {str(e)}")
        return False

def main():
    """Funzione principale del test."""
    print("🧪 Test BOBINA_VUOTA - Inserimento metri posati")
    print("=" * 50)
    
    # Attendi che il server sia pronto
    print("⏳ Attesa avvio del server...")
    time.sleep(5)
    
    # Test di login
    token = test_login()
    if not token:
        print("❌ Test fallito: impossibile ottenere il token di autenticazione")
        sys.exit(1)
    
    # Test inserimento metri posati con BOBINA_VUOTA
    success = test_metri_posati_bobina_vuota(token)
    if not success:
        print("❌ Test fallito: errore durante l'inserimento metri posati")
        sys.exit(1)
    
    # Verifica stato del cavo
    success = test_verifica_cavo(token)
    if not success:
        print("❌ Test fallito: errore durante la verifica del cavo")
        sys.exit(1)
    
    print("\n✅ Tutti i test sono stati completati con successo!")
    print("🎉 Il sistema gestisce correttamente BOBINA_VUOTA")

if __name__ == "__main__":
    main()
