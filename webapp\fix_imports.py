#!/usr/bin/env python3
"""
Script per correggere automaticamente le importazioni errate nel backend.
Sostituisce "webapp.backend" con "backend" in tutti i file Python.
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """Corregge le importazioni in un singolo file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern per trovare importazioni che iniziano con webapp.backend
        pattern = r'from webapp\.backend\.'
        replacement = 'from backend.'
        
        # Sostituisci le importazioni
        new_content = re.sub(pattern, replacement, content)
        
        # Verifica se ci sono state modifiche
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ Corretto: {file_path}")
            return True
        else:
            print(f"⏭️  Nessuna modifica necessaria: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Errore nel file {file_path}: {e}")
        return False

def find_and_fix_python_files(directory):
    """Trova e corregge tutti i file Python nella directory."""
    directory = Path(directory)
    fixed_count = 0
    total_count = 0
    
    # Trova tutti i file Python
    for file_path in directory.rglob("*.py"):
        # Salta i file __pycache__
        if "__pycache__" in str(file_path):
            continue
            
        total_count += 1
        if fix_imports_in_file(file_path):
            fixed_count += 1
    
    return fixed_count, total_count

def main():
    """Funzione principale."""
    print("🔧 Correzione automatica delle importazioni")
    print("=" * 50)
    
    # Directory del backend
    backend_dir = Path(__file__).parent / "backend"
    
    if not backend_dir.exists():
        print(f"❌ Directory backend non trovata: {backend_dir}")
        return
    
    print(f"📁 Scansione directory: {backend_dir}")
    
    # Correggi le importazioni
    fixed_count, total_count = find_and_fix_python_files(backend_dir)
    
    print("\n📊 Riepilogo:")
    print(f"File totali analizzati: {total_count}")
    print(f"File corretti: {fixed_count}")
    print(f"File non modificati: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print("\n🎉 Correzioni completate!")
    else:
        print("\n✅ Nessuna correzione necessaria.")

if __name__ == "__main__":
    main()
