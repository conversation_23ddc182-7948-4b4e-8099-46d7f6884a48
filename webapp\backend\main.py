from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

from backend.config import settings
from backend.api import api_router

# Assicurati che la directory static esista
os.makedirs(settings.STATIC_DIR, exist_ok=True)

app = FastAPI(
    title="CMS API",
    description="API per il sistema di gestione cantieri",
    version="1.0.0"
)

# Configurazione CORS per consentire le richieste dal frontend
print(f"Configurazione CORS con origini: {settings.CORS_ORIGINS}")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Consenti richieste da qualsiasi origine
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

@app.get("/")
async def root():
    """Endpoint di base per verificare che l'API sia in esecuzione."""
    return {"message": "CMS API è in esecuzione"}

@app.get("/api/health")
async def health_check():
    """Endpoint di health check per verificare che l'API sia in esecuzione e connessa al database."""
    from backend.database import get_db, engine
    from sqlalchemy import text

    try:
        # Verifica la connessione al database
        db = next(get_db())
        result = db.execute(text("SELECT 1")).scalar()

        if result == 1:
            return {
                "status": "ok",
                "message": "API in esecuzione e connessa al database",
                "database": "connected"
            }
        else:
            return {
                "status": "error",
                "message": "API in esecuzione ma problemi con il database",
                "database": "error"
            }
    except Exception as e:
        return {
            "status": "error",
            "message": f"API in esecuzione ma errore di connessione al database: {str(e)}",
            "database": "disconnected"
        }

# Includi i router delle API
app.include_router(api_router, prefix=settings.API_PREFIX)

# Monta la directory static per servire i file statici
app.mount("/static", StaticFiles(directory=str(settings.STATIC_DIR)), name="static")

if __name__ == "__main__":
    import uvicorn
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run the FastAPI backend server')
    parser.add_argument('--port', type=int, default=8001, help='Port to run the server on')
    args = parser.parse_args()

    print(f"Starting server on port {args.port}")
    uvicorn.run("main:app", host="0.0.0.0", port=args.port, reload=True)
