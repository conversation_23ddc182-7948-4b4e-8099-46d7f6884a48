import sqlite3
import logging
import os
import shutil
import threading
from datetime import datetime, date
from typing import Optional, Dict, Any, List, Tuple, Union
import bcrypt
from contextlib import contextmanager

# Configura il logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


class Config:
    """Classe di configurazione centralizzata per il database."""
    # Configurazione del database
    DEFAULT_DB_NAME = 'cantieri_fresh.db'
    BACKUP_DIR = 'backup'
    MAX_BACKUP_FILES = 10

    # Configurazione delle tabelle
    TABELLE_PRINCIPALI = ['Utenti', 'Cantieri', 'Cavi', 'parco_cavi', 'CertificazioniCavi', 'StrumentiCertificati', 'Comande']

    # Ruoli utente validi
    RUOLI_UTENTE = ['owner', 'user', 'cantieri_user']

    # Stati delle comande
    STATI_COMANDA = ['CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA', 'ANNULLATA']

    # Tipi di comande
    TIPI_COMANDA = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']

    # Codici di errore
    ERROR_CODES = {
        'duplicate_key': 'Violazione di unicità',
        'foreign_key': 'Violazione di chiave esterna',
        'general_error': 'Errore generico'
    }

    @classmethod
    def get_db_path(cls):
        """Restituisce il percorso del database, considerando le variabili d'ambiente."""
        return os.environ.get('DB_PATH', cls.DEFAULT_DB_NAME)

    @property
    def DB_NAME(self):
        """Proprietà per compatibilità con il codice esistente."""
        return self.get_db_path()


@contextmanager
def database_connection():
    """Context manager per gestire connessioni al database in modo sicuro.

    Utilizza l'istanza singleton di Database per ottenere una connessione.
    """
    db = Database()
    conn = None
    try:
        # Utilizziamo i convertitori personalizzati per date e timestamp
        conn = db.get_connection()
        yield conn
    except sqlite3.Error as e:
        logging.error(f"❌ Errore database: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()


# Adattatori e convertitori moderni per date e datetime in SQLite
# Questi sostituiscono gli adattatori predefiniti deprecati in Python 3.12

# Adattatore per datetime -> stringa ISO
def adapt_datetime(dt):
    return dt.isoformat()

# Adattatore per date -> stringa ISO
def adapt_date(d):
    return d.isoformat()

# Convertitore per stringa -> datetime
def convert_datetime(value):
    if value is None:
        return None
    return datetime.fromisoformat(value.decode())

# Convertitore per stringa -> date
def convert_date(value):
    if value is None:
        return None
    return date.fromisoformat(value.decode())

# Registrazione degli adattatori e convertitori
sqlite3.register_adapter(datetime, adapt_datetime)
sqlite3.register_adapter(date, adapt_date)
sqlite3.register_converter("TIMESTAMP", convert_datetime)
sqlite3.register_converter("DATE", convert_date)


class Database:
    """Classe per la gestione del database con pattern Singleton.
    Garantisce che esista una sola istanza della classe in tutta l'applicazione.
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, db_name=None):
        """Implementazione del pattern Singleton thread-safe.

        Args:
            db_name (str, optional): Nome del file del database. Se None, usa il valore predefinito da Config.

        Returns:
            Database: L'unica istanza della classe Database
        """
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(Database, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, db_name=None):
        """Inizializza la connessione al database.

        Args:
            db_name (str, optional): Nome del file del database. Se None, usa il valore predefinito da Config.
        """
        # Evita la reinizializzazione se l'istanza è già stata inizializzata
        if getattr(self, '_initialized', False):
            return

        self.db_name = db_name or Config.get_db_path()
        self._initialized = True
        self.inizializza_database()

    def get_connection(self):
        """Crea e restituisce una connessione al database.

        Returns:
            sqlite3.Connection: Connessione al database
        """
        conn = sqlite3.connect(
            self.db_name,
            detect_types=sqlite3.PARSE_DECLTYPES,
            isolation_level=None  # Autocommit mode
        )
        conn.row_factory = sqlite3.Row
        return conn

    def execute_transaction(self, queries: List[Tuple[str, Tuple]], description: str = None) -> bool:
        """Esegue una serie di query come una singola transazione.

        Args:
            queries: Lista di tuple (query, parametri)
            description: Descrizione opzionale della transazione per il logging

        Returns:
            bool: True se la transazione è stata completata con successo, False altrimenti
        """
        conn = None
        transaction_desc = f" '{description}'" if description else ""

        try:
            conn = self.get_connection()
            c = conn.cursor()

            # Disabilita temporaneamente l'autocommit
            conn.isolation_level = 'DEFERRED'

            # Log dell'inizio della transazione
            logging.debug(f"Inizio transazione{transaction_desc} con {len(queries)} query")

            # Esegui le query
            for i, (query, params) in enumerate(queries):
                try:
                    c.execute(query, params)
                    logging.debug(f"Query {i+1}/{len(queries)} eseguita con successo")
                except Exception as e:
                    logging.error(f"❌ Errore nell'esecuzione della query {i+1}/{len(queries)}: {str(e)}")
                    logging.error(f"Query: {query}")
                    logging.error(f"Parametri: {params}")
                    raise

            # Commit della transazione
            conn.commit()
            logging.info(f"✅ Transazione{transaction_desc} completata con successo ({len(queries)} query)")
            return True

        except Exception as e:
            if conn:
                conn.rollback()
                logging.warning(f"⚠️ Rollback della transazione{transaction_desc} eseguito")
            error_type = self.handle_db_error(f"transazione{transaction_desc}", e)
            return False

        finally:
            if conn:
                conn.close()

    def inizializza_database(self):
        """Inizializza il database creando le tabelle necessarie se non esistono."""
        try:
            with self.get_connection() as conn:
                c = conn.cursor()

                # Creazione tabella Utenti (invariata)
                c.execute('''CREATE TABLE IF NOT EXISTS Utenti (
                    id_utente INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    ruolo TEXT NOT NULL CHECK (ruolo IN ('owner', 'user', 'cantieri_user')),
                    data_scadenza DATE,
                    abilitato BOOLEAN DEFAULT 1,
                    created_by INTEGER,
                    FOREIGN KEY (created_by) REFERENCES Utenti(id_utente)
                )''')

                # Creazione tabella Cantieri
                c.execute('''CREATE TABLE IF NOT EXISTS Cantieri (
                    id_cantiere INTEGER PRIMARY KEY AUTOINCREMENT,
                    nome TEXT NOT NULL,
                    descrizione TEXT,
                    data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    password_cantiere TEXT NOT NULL,
                    id_utente INTEGER NOT NULL,
                    codice_univoco TEXT UNIQUE NOT NULL,
                    FOREIGN KEY (id_utente) REFERENCES Utenti(id_utente)
                )''')

                # Creazione tabella Cavi (aggiornata)
                c.execute('''CREATE TABLE IF NOT EXISTS Cavi (
                    id_cavo TEXT NOT NULL,
                    id_cantiere INTEGER NOT NULL,
                    revisione_ufficiale TEXT NOT NULL DEFAULT '00',
                    sistema TEXT,
                    utility TEXT NOT NULL,
                    colore_cavo TEXT,
                    tipologia TEXT NOT NULL,
                    n_conduttori INTEGER NOT NULL,
                    sezione TEXT NOT NULL,
                    SH TEXT,
                    ubicazione_partenza TEXT NOT NULL,
                    utenza_partenza TEXT,
                    descrizione_utenza_partenza TEXT,
                    ubicazione_arrivo TEXT NOT NULL,
                    utenza_arrivo TEXT,
                    descrizione_utenza_arrivo TEXT,
                    metri_teorici REAL NOT NULL,
                    metratura_reale REAL DEFAULT 0,
                    responsabile_posa TEXT,
                    id_bobina TEXT,
                    stato_installazione TEXT NOT NULL,
                    modificato_manualmente INTEGER DEFAULT 0,
                    data_posa DATE,
                    collegamenti INTEGER DEFAULT 0,  -- Flag per i collegamenti: 0=nessuno, 1=partenza, 2=arrivo, 3=entrambi
                    responsabile_partenza TEXT,      -- Responsabile del collegamento lato partenza
                    responsabile_arrivo TEXT,        -- Responsabile del collegamento lato arrivo
                    comanda_posa TEXT,               -- Riferimento alla comanda di posa
                    comanda_partenza TEXT,           -- Riferimento alla comanda di collegamento partenza
                    comanda_arrivo TEXT,             -- Riferimento alla comanda di collegamento arrivo
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id_cavo, id_cantiere),  -- Chiave primaria composta
                    FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                    FOREIGN KEY (id_bobina) REFERENCES parco_cavi(ID_BOBINA)
                )''')

                # Creazione tabella parco_cavi (invariata)
                c.execute('''CREATE TABLE IF NOT EXISTS parco_cavi (
                    ID_BOBINA TEXT PRIMARY KEY,
                    numero_bobina TEXT NOT NULL,
                    utility TEXT NOT NULL,
                    tipologia TEXT NOT NULL,
                    n_conduttori INTEGER NOT NULL,
                    sezione REAL NOT NULL,
                    metri_totali REAL NOT NULL,
                    metri_residui REAL NOT NULL,
                    stato_bobina TEXT NOT NULL,
                    ubicazione_bobina TEXT,
                    fornitore TEXT,
                    n_DDT TEXT,
                    data_DDT DATE,
                    configurazione TEXT,
                    id_cantiere INTEGER,  -- Nuova colonna per collegare la bobina a un cantiere
                    FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE SET NULL  -- Vincolo di chiave esterna
                )''')

                # La tabella StoricoPosa è stata rimossa, i dati di posa sono ora nella tabella Cavi

                # Tabella StrumentiCertificati per gestire gli strumenti di misura certificati
                c.execute('''
                    CREATE TABLE IF NOT EXISTS StrumentiCertificati (
                        id_strumento INTEGER PRIMARY KEY AUTOINCREMENT,
                        id_cantiere INTEGER NOT NULL,
                        nome TEXT NOT NULL,
                        marca TEXT NOT NULL,
                        modello TEXT NOT NULL,
                        numero_serie TEXT NOT NULL,
                        data_calibrazione DATE NOT NULL,
                        data_scadenza_calibrazione DATE NOT NULL,
                        certificato_calibrazione TEXT,  -- Percorso al file del certificato
                        note TEXT,
                        timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        timestamp_modifica TIMESTAMP,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
                    )
                ''')

                # Tabella CertificazioniCavi per gestire le certificazioni dei cavi
                c.execute('''
                    CREATE TABLE IF NOT EXISTS CertificazioniCavi (
                        id_certificazione INTEGER PRIMARY KEY AUTOINCREMENT,
                        id_cantiere INTEGER NOT NULL,
                        id_cavo TEXT NOT NULL,
                        numero_certificato TEXT NOT NULL,  -- Numero progressivo univoco del certificato
                        data_certificazione DATE NOT NULL,
                        id_operatore TEXT,  -- Operatore che ha eseguito i test
                        strumento_utilizzato TEXT,  -- Descrizione dello strumento utilizzato
                        id_strumento INTEGER,  -- Riferimento allo strumento certificato
                        lunghezza_misurata REAL,  -- in metri
                        valore_continuita TEXT,  -- Valore del test di continuità
                        valore_isolamento TEXT,  -- Valore del test di isolamento in MΩ
                        valore_resistenza TEXT,  -- Valore del test di resistenza
                        percorso_certificato TEXT,  -- Percorso file PDF del certificato
                        percorso_foto TEXT,  -- Percorso file della foto
                        note TEXT,
                        timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        timestamp_modifica TIMESTAMP,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_cavo, id_cantiere) REFERENCES Cavi(id_cavo, id_cantiere) ON DELETE CASCADE,
                        FOREIGN KEY (id_strumento) REFERENCES StrumentiCertificati(id_strumento),
                        UNIQUE (id_cantiere, id_cavo)  -- Vincolo di unicità per evitare duplicati
                    )
                ''')

                # I collegamenti sono ora gestiti direttamente nella tabella Cavi tramite il campo 'collegamenti'

                # Tabella Comande per gestire le comande di lavoro
                c.execute('''
                    CREATE TABLE IF NOT EXISTS Comande (
                        codice_comanda VARCHAR(50) PRIMARY KEY,
                        tipo_comanda VARCHAR(20) NOT NULL,  -- 'POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'
                        descrizione TEXT,
                        data_creazione DATE NOT NULL,
                        data_scadenza DATE,
                        responsabile VARCHAR(100),
                        stato VARCHAR(20) NOT NULL,  -- 'CREATA', 'ASSEGNATA', 'IN_CORSO', 'COMPLETATA'
                        id_cantiere INTEGER NOT NULL,
                        FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
                    )
                ''')

                # Creazione indici ottimizzati - ridotti ai più essenziali

                # Indici fondamentali per relazioni e filtri frequenti
                c.execute('''CREATE INDEX IF NOT EXISTS idx_cantieri_id_utente ON Cantieri(id_utente)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_id_cantiere ON Cavi(id_cantiere)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_id_bobina ON Cavi(id_bobina)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_data_posa ON Cavi(data_posa)''')

                # Indici composti per query di join frequenti
                # Questi sostituiscono alcuni indici singoli ridondanti
                c.execute('''CREATE INDEX IF NOT EXISTS idx_parco_cavi_id_cantiere_numero_bobina
                             ON parco_cavi(id_cantiere, numero_bobina)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_id_cantiere_id_cavo
                             ON Cavi(id_cantiere, id_cavo)''')

                # Indici per StrumentiCertificati - mantenuti solo i più importanti
                c.execute('''CREATE INDEX IF NOT EXISTS idx_strumenti_id_cantiere
                             ON StrumentiCertificati(id_cantiere)''')

                # Indici per CertificazioniCavi - ottimizzati per le query più frequenti
                c.execute('''CREATE INDEX IF NOT EXISTS idx_certificazioni_id_cantiere_id_cavo
                             ON CertificazioniCavi(id_cantiere, id_cavo)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_certificazioni_numero
                             ON CertificazioniCavi(numero_certificato)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_certificazioni_data
                             ON CertificazioniCavi(data_certificazione)''')

                # Indici per le comande - mantenuti solo quelli essenziali
                c.execute('''CREATE INDEX IF NOT EXISTS idx_comande_id_cantiere_stato
                             ON Comande(id_cantiere, stato)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_comanda_posa
                             ON Cavi(comanda_posa) WHERE comanda_posa IS NOT NULL''')

                # Indice per il campo collegamenti nella tabella Cavi - solo se effettivamente utilizzato
                c.execute('''CREATE INDEX IF NOT EXISTS idx_cavi_collegamenti
                             ON Cavi(collegamenti) WHERE collegamenti IS NOT NULL''')

                conn.commit()
                logging.info("✅ Database inizializzato con successo")
                return True

        except Exception as e:
            logging.error(f"❌ Errore durante l'inizializzazione del database: {str(e)}")
            return False

    def verifica_integrita_database(self):
        """Verifica l'integrità del database."""
        try:
            with self.get_connection() as conn:
                c = conn.cursor()

                tabelle_richieste = {
                    'Cantieri': [
                        'id_cantiere',
                        'nome',
                        'descrizione',
                        'data_creazione',
                        'password_cantiere',
                        'id_utente',
                        'codice_univoco'
                    ],
                    'Cavi': [
                        'id_cavo',
                        'id_cantiere',
                        'revisione_ufficiale',
                        'sistema',
                        'utility',
                        'colore_cavo',
                        'tipologia',
                        'n_conduttori',
                        'sezione',
                        'SH',
                        'ubicazione_partenza',
                        'utenza_partenza',
                        'descrizione_utenza_partenza',
                        'ubicazione_arrivo',
                        'utenza_arrivo',
                        'descrizione_utenza_arrivo',
                        'metri_teorici',
                        'metratura_reale',
                        'responsabile_posa',
                        'id_bobina',
                        'stato_installazione',
                        'modificato_manualmente',
                        'data_posa',
                        'collegamenti',
                        'responsabile_partenza',
                        'responsabile_arrivo',
                        'comanda_posa',
                        'comanda_partenza',
                        'comanda_arrivo',
                        'timestamp'
                    ],
                    'parco_cavi': [
                        'ID_BOBINA', 'numero_bobina', 'utility', 'tipologia',
                        'n_conduttori', 'sezione', 'metri_totali', 'metri_residui',
                        'stato_bobina', 'ubicazione_bobina', 'fornitore',
                        'n_DDT', 'data_DDT', 'configurazione',
                        'id_cantiere'  # Aggiunta la nuova colonna
                    ],
                    'Utenti': [
                        'id_utente', 'username', 'password', 'ruolo',
                        'data_scadenza', 'abilitato', 'created_by'
                    ],
                    'Comande': [
                        'codice_comanda',
                        'tipo_comanda',
                        'descrizione',
                        'data_creazione',
                        'data_scadenza',
                        'responsabile',
                        'stato',
                        'id_cantiere'
                    ],
                    'CertificazioniCavi': [
                        'id_certificazione',
                        'id_cantiere',
                        'id_cavo',
                        'numero_certificato',
                        'data_certificazione',
                        'id_operatore',
                        'strumento_utilizzato',
                        'id_strumento',
                        'lunghezza_misurata',
                        'valore_continuita',
                        'valore_isolamento',
                        'valore_resistenza',
                        'percorso_certificato',
                        'percorso_foto',
                        'note',
                        'timestamp_creazione',
                        'timestamp_modifica'
                    ],
                    'StrumentiCertificati': [
                        'id_strumento',
                        'id_cantiere',
                        'nome',
                        'marca',
                        'modello',
                        'numero_serie',
                        'data_calibrazione',
                        'data_scadenza_calibrazione',
                        'certificato_calibrazione',
                        'note',
                        'timestamp_creazione',
                        'timestamp_modifica'
                    ]
                }

                problemi = []
                for tabella, colonne_attese in tabelle_richieste.items():
                    # Verifica esistenza tabella
                    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (tabella,))
                    if not c.fetchone():
                        problemi.append(f"⚠️ Tabella mancante: {tabella}")
                        continue

                    # Verifica colonne
                    c.execute(f"PRAGMA table_info({tabella})")
                    colonne_esistenti = {row[1] for row in c.fetchall()}
                    colonne_mancanti = set(colonne_attese) - colonne_esistenti
                    if colonne_mancanti:
                        problemi.append(f"⚠️ Colonne mancanti in {tabella}: {colonne_mancanti}")

                    # Verifica vincoli per la tabella Utenti
                    if tabella == 'Utenti':
                        c.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='Utenti'")
                        create_sql = c.fetchone()[0]
                        if "CHECK (ruolo IN ('owner', 'user', 'cantieri_user'))" not in create_sql:
                            problemi.append("⚠️ Manca il vincolo CHECK sui ruoli nella tabella Utenti")
                        if "FOREIGN KEY (created_by) REFERENCES Utenti(id_utente)" not in create_sql:
                            problemi.append("⚠️ Manca il vincolo FOREIGN KEY per created_by nella tabella Utenti")

                if problemi:
                    for problema in problemi:
                        logging.warning(problema)
                    return False

                logging.info("✅ Database integro")
                return True

        except Exception as e:
            logging.error(f"❌ Errore durante la verifica del database: {str(e)}")
            return False

    def reset_database(self) -> bool:
        """Resetta completamente il database eliminando tutti i dati.

        Returns:
            bool: True se il reset è avvenuto con successo, False altrimenti
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Ottieni tutte le tabelle dal database
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tabelle = [row[0] for row in cursor.fetchall()]

                # Disabilita temporaneamente i vincoli di foreign key per evitare errori
                cursor.execute("PRAGMA foreign_keys = OFF")

                # Elimina i dati da tutte le tabelle in ordine inverso di dipendenza
                # Prima le tabelle dipendenti, poi quelle principali

                # Tabelle dipendenti (con foreign keys)
                for tabella in ['CertificazioniCavi', 'StrumentiCertificati', 'Comande', 'Cavi']:
                    if tabella in tabelle:
                        cursor.execute(f"DELETE FROM {tabella}")
                        logging.info(f"✅ Dati eliminati dalla tabella {tabella}")

                # Tabelle intermedie
                for tabella in ['parco_cavi', 'Cantieri']:
                    if tabella in tabelle:
                        cursor.execute(f"DELETE FROM {tabella}")
                        logging.info(f"✅ Dati eliminati dalla tabella {tabella}")

                # Tabella principale (Utenti)
                if 'Utenti' in tabelle:
                    cursor.execute("DELETE FROM Utenti")
                    logging.info("✅ Dati eliminati dalla tabella Utenti")

                # Riabilita i vincoli di foreign key
                cursor.execute("PRAGMA foreign_keys = ON")

                conn.commit()
                logging.info("✅ Database resettato con successo")
                return True

        except sqlite3.Error as e:
            logging.error(f"❌ Errore durante il reset del database: {str(e)}")
            return False

    # La funzione aggiorna_database_per_comande è stata rimossa perché non più necessaria
    # La tabella Comande e i relativi campi sono già definiti nella funzione inizializza_database

    def handle_db_error(self, operation: str, error: Exception) -> str:
        """
        Gestisce gli errori del database in modo centralizzato.

        Args:
            operation: Descrizione dell'operazione che ha generato l'errore
            error: L'eccezione catturata

        Returns:
            str: Codice di errore standardizzato
        """
        error_code = getattr(error, 'sqlite_errorcode', None)
        error_message = str(error)

        if error_code == 19:  # SQLITE_CONSTRAINT
            if "UNIQUE constraint failed" in error_message:
                logging.error(f"❌ Violazione di unicità durante {operation}: {error_message}")
                return "duplicate_key"
            elif "FOREIGN KEY constraint failed" in error_message:
                logging.error(f"❌ Violazione di chiave esterna durante {operation}: {error_message}")
                return "foreign_key"

        logging.error(f"❌ Errore durante {operation}: {error_message}")
        return "general_error"

    def backup_database(self, backup_path: str = None) -> Optional[str]:
        """
        Crea un backup del database.

        Args:
            backup_path: Percorso del file di backup. Se None, viene generato automaticamente.

        Returns:
            str: Percorso del file di backup creato, None in caso di errore
        """
        try:
            # Crea la directory di backup se non esiste
            os.makedirs(Config.BACKUP_DIR, exist_ok=True)

            # Genera il nome del file di backup se non specificato
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = os.path.join(Config.BACKUP_DIR, f"backup_{timestamp}.db")

            # Chiudi tutte le connessioni al database
            conn = None
            try:
                # Crea una connessione temporanea per assicurarsi che il database sia accessibile
                conn = sqlite3.connect(self.db_name)
                conn.close()
            except Exception:
                if conn:
                    conn.close()
                raise

            # Copia il file del database
            shutil.copy2(self.db_name, backup_path)

            # Elimina i backup più vecchi se ce ne sono troppi
            self._cleanup_old_backups()

            logging.info(f"✅ Backup creato con successo: {backup_path}")
            return backup_path

        except Exception as e:
            logging.error(f"❌ Errore durante il backup del database: {str(e)}")
            return None

    def _cleanup_old_backups(self) -> None:
        """
        Elimina i backup più vecchi se ce ne sono troppi.
        """
        try:
            # Ottieni la lista dei file di backup
            backup_files = [os.path.join(Config.BACKUP_DIR, f) for f in os.listdir(Config.BACKUP_DIR)
                           if f.startswith("backup_") and f.endswith(".db")]

            # Ordina i file per data di modifica (dal più vecchio al più recente)
            backup_files.sort(key=lambda x: os.path.getmtime(x))

            # Elimina i backup più vecchi se ce ne sono troppi
            if len(backup_files) > Config.MAX_BACKUP_FILES:
                files_to_delete = backup_files[:len(backup_files) - Config.MAX_BACKUP_FILES]
                for file_path in files_to_delete:
                    os.remove(file_path)
                    logging.info(f"Backup obsoleto eliminato: {file_path}")
        except Exception as e:
            logging.warning(f"Errore durante la pulizia dei backup: {str(e)}")

    def restore_database(self, backup_path: str) -> bool:
        """
        Ripristina il database da un backup.

        Args:
            backup_path: Percorso del file di backup

        Returns:
            bool: True se il ripristino è avvenuto con successo, False altrimenti
        """
        try:
            # Verifica che il file di backup esista
            if not os.path.exists(backup_path):
                logging.error(f"❌ File di backup non trovato: {backup_path}")
                return False

            # Crea un backup del database corrente prima del ripristino
            current_backup = self.backup_database()
            if not current_backup:
                logging.warning("⚠️ Impossibile creare un backup del database corrente prima del ripristino")

            # Chiudi tutte le connessioni al database
            conn = None
            try:
                # Crea una connessione temporanea per assicurarsi che il database sia accessibile
                conn = sqlite3.connect(self.db_name)
                conn.close()
            except Exception:
                if conn:
                    conn.close()
                raise

            # Ripristina il database dal backup
            shutil.copy2(backup_path, self.db_name)

            logging.info(f"✅ Database ripristinato con successo da: {backup_path}")
            return True

        except Exception as e:
            logging.error(f"❌ Errore durante il ripristino del database: {str(e)}")
            return False



    def visualizza_database_raw(self, tabelle_filtro: List[str] = None, max_records: int = 100) -> bool:
        """
        Visualizza il contenuto raw di tutte le tabelle del database, mostrando la struttura e i dati grezzi.

        Args:
            tabelle_filtro: Lista opzionale di nomi di tabelle da visualizzare. Se None, visualizza tutte le tabelle.
            max_records: Numero massimo di record da visualizzare per tabella. Default: 100.

        Returns:
            bool: True se la visualizzazione è avvenuta con successo, False altrimenti
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
                tabelle = cursor.fetchall()

                if not tabelle:
                    print("\n⚠️ Nessuna tabella trovata nel database")
                    return True

                # Filtra le tabelle se specificato
                if tabelle_filtro:
                    tabelle = [t for t in tabelle if t[0] in tabelle_filtro]
                    if not tabelle:
                        print(f"\n⚠️ Nessuna delle tabelle specificate trovata nel database")
                        return True

                for tabella in tabelle:
                    nome_tabella = tabella[0]
                    print(f"\n{'=' * 80}")
                    print(f"TABELLA: {nome_tabella}")
                    print(f"{'=' * 80}")

                    # Ottieni informazioni sulla struttura della tabella
                    cursor.execute(f"PRAGMA table_info({nome_tabella})")
                    colonne = cursor.fetchall()

                    if colonne:
                        print("\nSTRUTTURA:")
                        print("-" * 80)
                        print(f"{'ID':<3} {'Nome':<20} {'Tipo':<15} {'NotNull':<8} {'Default':<15} {'PK':<3}")
                        print("-" * 80)

                        for col in colonne:
                            print(f"{col['cid']:<3} {col['name']:<20} {col['type']:<15} {col['notnull']:<8} {str(col['dflt_value']):<15} {col['pk']:<3}")

                    # Ottieni i dati grezzi
                    cursor.execute(f"SELECT * FROM {nome_tabella} LIMIT {max_records+1}")
                    records = cursor.fetchall()

                    if not records:
                        print("\nDATI: TABELLA VUOTA")
                        continue

                    # Stampa records in formato più leggibile
                    print("\nDATI:")
                    print("-" * 80)

                    # Stampa intestazioni colonne
                    headers = [col['name'] for col in colonne]
                    header_str = ' | '.join(f"{h}" for h in headers)
                    print(header_str)
                    print("-" * 80)

                    # Stampa i record
                    count = 0
                    for record in records:
                        if count >= max_records:
                            print(f"\n... e altri record (limitato a {max_records} record)")
                            break

                        # Converti il record in una lista di stringhe
                        values = []
                        for key in headers:
                            val = record[key]
                            # Formatta il valore per la visualizzazione
                            if val is None:
                                val_str = "NULL"
                            elif isinstance(val, (int, float)):
                                val_str = str(val)
                            else:
                                # Tronca stringhe lunghe
                                val_str = str(val)
                                if len(val_str) > 20:
                                    val_str = val_str[:17] + "..."
                            values.append(val_str)

                        row_str = ' | '.join(values)
                        print(row_str)
                        count += 1

                return True

        except Exception as e:
            logging.error(f"❌ Errore durante la visualizzazione raw del database: {str(e)}")
            print(f"\n❌ Errore durante la visualizzazione raw del database: {e}")
            return False


