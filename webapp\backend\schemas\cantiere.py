from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class CantiereBase(BaseModel):
    """Schema base per i cantieri."""
    nome: str
    descrizione: Optional[str] = None

class CantiereCreate(CantiereBase):
    """Schema per la creazione di un cantiere."""
    password_cantiere: str

class CantiereUpdate(BaseModel):
    """Schema per l'aggiornamento di un cantiere."""
    nome: Optional[str] = None
    descrizione: Optional[str] = None
    password_cantiere: Optional[str] = None

class CantiereInDB(CantiereBase):
    """Schema per un cantiere nel database."""
    id_cantiere: int
    data_creazione: datetime
    id_utente: int
    codice_univoco: str

    class Config:
        orm_mode = True

class PasswordVerifyRequest(BaseModel):
    """Schema per la verifica della password del cantiere."""
    password_attuale: str

class PasswordChangeRequest(BaseModel):
    """Schema per il cambio password del cantiere."""
    password_attuale: str
    password_nuova: str
    conferma_password: str

class PasswordVerifyResponse(BaseModel):
    """Schema per la risposta della verifica password."""
    password_corretta: bool
    password_cantiere: Optional[str] = None  # Solo se la verifica è corretta

class PasswordChangeResponse(BaseModel):
    """Schema per la risposta del cambio password."""
    success: bool
    message: str

class PasswordViewResponse(BaseModel):
    """Schema per la risposta della visualizzazione password diretta."""
    password_cantiere: str
