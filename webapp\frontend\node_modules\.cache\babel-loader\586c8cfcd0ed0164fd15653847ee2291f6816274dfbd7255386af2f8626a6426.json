{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Stack, MenuItem, Alert, CircularProgress, Typography, Divider, Paper, IconButton, Tooltip, Grid } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon, Cancel as CancelIcon, KeyboardArrowDown as KeyboardArrowDownIcon, KeyboardArrowUp as KeyboardArrowUpIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SECTION_CONFIG = [{\n  title: 'Informazioni Generali',\n  collapsible: false,\n  fields: [{\n    name: 'id_cavo',\n    label: 'ID Cavo',\n    required: true,\n    inputProps: {\n      style: {\n        textTransform: 'uppercase'\n      }\n    }\n  }, {\n    name: 'utility',\n    label: 'Utility',\n    required: true\n  }, {\n    name: 'sistema',\n    label: 'Sistema'\n  }]\n}, {\n  title: 'Caratteristiche Tecniche',\n  collapsible: false,\n  fields: [{\n    name: 'colore_cavo',\n    label: 'Colore Cavo'\n  }, {\n    name: 'tipologia',\n    label: 'Tipologia'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    name: 'sezione',\n    label: 'Formazione'\n  }\n  // sh field is now a spare field (kept in DB but hidden in UI)\n  ]\n}, {\n  title: 'Partenza',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_partenza',\n    label: 'Ubicazione Partenza',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'utenza_partenza',\n    label: 'Utenza Partenza',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'descrizione_utenza_partenza',\n    label: 'Descrizione Utenza Partenza',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }]\n}, {\n  title: 'Arrivo',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_arrivo',\n    label: 'Ubicazione Arrivo',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'utenza_arrivo',\n    label: 'Utenza Arrivo',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'descrizione_utenza_arrivo',\n    label: 'Descrizione Utenza Arrivo',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }]\n}, {\n  title: 'Metratura',\n  collapsible: false,\n  fields: [{\n    name: 'metri_teorici',\n    label: 'Metri Teorici',\n    required: true\n  }]\n}];\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\nconst CavoForm = ({\n  mode = 'add',\n  initialData = {},\n  onSubmit,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    ...defaultData,\n    ...initialData\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n  const [showWarnings, setShowWarnings] = useState(false);\n\n  // Inizializza lo stato di espansione delle sezioni collassabili\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => {\n      if (s.collapsible) state[s.title] = true;\n    });\n    setExpanded(state);\n  }, []);\n\n  // Gestisce l'espansione/collasso delle sezioni\n  const toggleExpand = title => {\n    setExpanded(prev => ({\n      ...prev,\n      [title]: !prev[title]\n    }));\n  };\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleChange = e => {\n    var _result$message;\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    const extra = name === 'metratura_reale' ? {\n      metriTeorici: parseFloat(formData.metri_teorici || 0)\n    } : {};\n    const result = validateField(name, value, extra);\n\n    // Assicurati che gli errori siano sempre stringhe o null\n    const errorMessage = result.valid ? null : typeof result.message === 'object' ? ((_result$message = result.message) === null || _result$message === void 0 ? void 0 : _result$message.message) || JSON.stringify(result.message) : result.message;\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: errorMessage\n    }));\n    setWarnings(prev => {\n      var _result$message2;\n      // Assicurati che i warning siano sempre stringhe o null\n      const warningMessage = result.warning ? typeof result.message === 'object' ? ((_result$message2 = result.message) === null || _result$message2 === void 0 ? void 0 : _result$message2.message) || JSON.stringify(result.message) : result.message : null;\n      const newWarnings = {\n        ...prev,\n        [name]: warningMessage\n      };\n      // Mostra il banner di avviso se ci sono warning\n      setShowWarnings(Object.values(newWarnings).some(w => w));\n      return newWarnings;\n    });\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n    if (!validation.isValid) {\n      // Assicurati che gli errori siano sempre stringhe o null\n      const processedErrors = {};\n      Object.entries(validation.errors).forEach(([key, value]) => {\n        processedErrors[key] = typeof value === 'object' ? (value === null || value === void 0 ? void 0 : value.message) || JSON.stringify(value) : value;\n      });\n\n      // Assicurati che i warning siano sempre stringhe o null\n      const processedWarnings = {};\n      Object.entries(validation.warnings).forEach(([key, value]) => {\n        processedWarnings[key] = typeof value === 'object' ? (value === null || value === void 0 ? void 0 : value.message) || JSON.stringify(value) : value;\n      });\n      setFormErrors(processedErrors);\n      setWarnings(processedWarnings);\n      setShowWarnings(Object.values(processedWarnings).some(w => w));\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n    try {\n      const finalData = {\n        ...validation.validatedData,\n        id_cavo: validation.validatedData.id_cavo.toUpperCase(),\n        metratura_reale: '0' // Set default value\n      };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n\n  // Gestisce l'annullamento del form\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    } else {\n      redirectToVisualizzaCavi(navigate);\n    }\n  };\n\n  // Renderizza un singolo campo del form\n  const renderField = field => {\n    var _formData$field$name, _field$options;\n    // Gestisce i valori null/undefined per evitare la visualizzazione di \"null\"\n    const fieldValue = (_formData$field$name = formData[field.name]) !== null && _formData$field$name !== void 0 ? _formData$field$name : '';\n\n    // Gestisce i messaggi di errore e warning in modo più pulito\n    const errorMessage = formErrors[field.name];\n    const warningMessage = warnings[field.name];\n    const helperText = errorMessage || warningMessage || '';\n    return /*#__PURE__*/_jsxDEV(TextField, {\n      select: field.type === 'select',\n      fullWidth: true,\n      size: \"small\",\n      margin: \"none\",\n      label: field.label,\n      name: field.name,\n      value: fieldValue,\n      onChange: handleChange,\n      error: !!errorMessage,\n      helperText: helperText,\n      required: field.required,\n      variant: \"outlined\",\n      InputLabelProps: {\n        shrink: true,\n        sx: {\n          fontWeight: field.required ? 600 : 400,\n          color: field.required ? 'primary.main' : 'text.secondary'\n        }\n      },\n      sx: {\n        '& .MuiOutlinedInput-root': {\n          '&:hover fieldset': {\n            borderColor: 'primary.main'\n          },\n          '&.Mui-focused fieldset': {\n            borderWidth: 2\n          }\n        },\n        '& .MuiFormHelperText-root': {\n          fontSize: '0.75rem',\n          marginTop: '4px',\n          color: errorMessage ? 'error.main' : warningMessage ? 'warning.main' : 'text.secondary'\n        }\n      },\n      ...(field.inputProps || {}),\n      children: (_field$options = field.options) === null || _field$options === void 0 ? void 0 : _field$options.map(opt => /*#__PURE__*/_jsxDEV(MenuItem, {\n        value: opt,\n        children: opt\n      }, opt, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 2,\n    sx: {\n      border: '1px solid #e0e0e0',\n      borderRadius: 3,\n      overflow: 'hidden',\n      background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',\n      boxShadow: '0 4px 20px rgba(0,0,0,0.08)'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      component: \"form\",\n      onSubmit: handleSubmit,\n      noValidate: true,\n      children: [showWarnings && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 19\n        }, this),\n        sx: {\n          borderRadius: 0,\n          py: 0.5\n        },\n        children: \"Alcuni campi potrebbero necessitare revisione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), SECTION_CONFIG.map((section, index) => {\n        const isExpanded = section.collapsible ? expanded[section.title] : true;\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            borderBottom: index < SECTION_CONFIG.length - 1 ? '0.5px solid #e0e0e0' : 'none'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              px: 2,\n              py: 0.8,\n              bgcolor: '#f5f5f5',\n              borderBottom: isExpanded ? '0.5px solid #e0e0e0' : 'none',\n              cursor: section.collapsible ? 'pointer' : 'default'\n            },\n            onClick: () => section.collapsible && toggleExpand(section.title),\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                color: '#333',\n                fontSize: '0.9rem'\n              },\n              children: section.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), section.collapsible && /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              sx: {\n                ml: 1\n              },\n              onClick: e => {\n                e.stopPropagation();\n                toggleExpand(section.title);\n              },\n              children: isExpanded ? /*#__PURE__*/_jsxDEV(KeyboardArrowUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(KeyboardArrowDownIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 61\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), isExpanded && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 1.2\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: section.fields.map(field => {\n                var _field$gridSize, _field$gridSize2, _field$gridSize3;\n                return /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: ((_field$gridSize = field.gridSize) === null || _field$gridSize === void 0 ? void 0 : _field$gridSize.xs) || 12,\n                  sm: ((_field$gridSize2 = field.gridSize) === null || _field$gridSize2 === void 0 ? void 0 : _field$gridSize2.sm) || 6,\n                  md: ((_field$gridSize3 = field.gridSize) === null || _field$gridSize3 === void 0 ? void 0 : _field$gridSize3.md) || 4,\n                  children: renderField(field)\n                }, field.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 17\n          }, this)]\n        }, section.title, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 1.5,\n          p: 1.2,\n          bgcolor: '#f9f9f9',\n          borderTop: '0.5px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Annulla e torna indietro\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"inherit\",\n            onClick: handleCancel,\n            disabled: loading,\n            startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 26\n            }, this),\n            size: \"small\",\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: mode === 'add' ? 'Salva nuovo cavo' : 'Salva modifiche al cavo',\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 85\n            }, this),\n            disabled: loading,\n            size: \"small\",\n            children: loading ? 'Salvataggio...' : mode === 'add' ? 'Salva' : 'Salva'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s(CavoForm, \"ALEC1doFADxWA7KR9oJ+Ttc3ZMU=\", false, function () {\n  return [useNavigate];\n});\n_c = CavoForm;\nexport default CavoForm;\nvar _c;\n$RefreshReg$(_c, \"CavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Typography", "Divider", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "Grid", "Save", "SaveIcon", "Warning", "WarningIcon", "Cancel", "CancelIcon", "KeyboardArrowDown", "KeyboardArrowDownIcon", "KeyboardArrowUp", "KeyboardArrowUpIcon", "useNavigate", "validateCavoData", "validateField", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "SECTION_CONFIG", "title", "collapsible", "fields", "name", "label", "required", "inputProps", "style", "textTransform", "gridSize", "xs", "sm", "md", "defaultData", "id_cavo", "utility", "sistema", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "CavoForm", "mode", "initialData", "onSubmit", "onSuccess", "onError", "onCancel", "_s", "navigate", "formData", "setFormData", "formErrors", "setFormErrors", "warnings", "setWarnings", "loading", "setLoading", "expanded", "setExpanded", "showWarnings", "setShowWarnings", "state", "for<PERSON>ach", "s", "toggleExpand", "prev", "handleChange", "e", "_result$message", "value", "target", "extra", "metriTeorici", "parseFloat", "result", "errorMessage", "valid", "message", "JSON", "stringify", "_result$message2", "warningMessage", "warning", "newWarnings", "Object", "values", "some", "w", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "processedErrors", "entries", "errors", "key", "processedWarnings", "finalData", "validatedData", "toUpperCase", "err", "handleCancel", "renderField", "field", "_formData$field$name", "_field$options", "fieldValue", "helperText", "select", "type", "fullWidth", "size", "margin", "onChange", "error", "variant", "InputLabelProps", "shrink", "sx", "fontWeight", "color", "borderColor", "borderWidth", "fontSize", "marginTop", "children", "options", "map", "opt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "elevation", "border", "borderRadius", "overflow", "background", "boxShadow", "component", "noValidate", "severity", "icon", "py", "section", "index", "isExpanded", "borderBottom", "length", "display", "justifyContent", "alignItems", "px", "bgcolor", "cursor", "onClick", "ml", "stopPropagation", "p", "container", "spacing", "_field$gridSize", "_field$gridSize2", "_field$gridSize3", "item", "gap", "borderTop", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Stack,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Typography,\n  Divider,\n  Paper,\n  IconButton,\n  Tooltip,\n  Grid\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Warning as WarningIcon,\n  Cancel as CancelIcon,\n  KeyboardArrowDown as KeyboardArrowDownIcon,\n  KeyboardArrowUp as KeyboardArrowUpIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst SECTION_CONFIG = [\n  {\n    title: 'Informazioni Generali',\n    collapsible: false,\n    fields: [\n      { name: 'id_cavo', label: 'ID Cavo', required: true, inputProps: { style: { textTransform: 'uppercase' } } },\n      { name: 'utility', label: 'Utility', required: true },\n      { name: 'sistema', label: '<PERSON>ste<PERSON>' }\n    ]\n  },\n  {\n    title: 'Caratteristiche Tecniche',\n    collapsible: false,\n    fields: [\n      { name: 'colore_cavo', label: 'Colore Cavo' },\n      { name: 'tipologia', label: 'Tipologia' },\n      // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n      { name: 'sezione', label: 'Formazione' },\n      // sh field is now a spare field (kept in DB but hidden in UI)\n    ]\n  },\n  {\n    title: 'Partenza',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_partenza', label: 'Ubicazione Partenza', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'utenza_partenza', label: 'Utenza Partenza', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'descrizione_utenza_partenza', label: 'Descrizione Utenza Partenza', gridSize: { xs: 12, sm: 6, md: 4 } }\n    ]\n  },\n  {\n    title: 'Arrivo',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_arrivo', label: 'Ubicazione Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'utenza_arrivo', label: 'Utenza Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'descrizione_utenza_arrivo', label: 'Descrizione Utenza Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } }\n    ]\n  },\n  {\n    title: 'Metratura',\n    collapsible: false,\n    fields: [\n      { name: 'metri_teorici', label: 'Metri Teorici', required: true }\n    ]\n  }\n];\n\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\n\nconst CavoForm = ({ mode = 'add', initialData = {}, onSubmit, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({ ...defaultData, ...initialData });\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n  const [showWarnings, setShowWarnings] = useState(false);\n\n  // Inizializza lo stato di espansione delle sezioni collassabili\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => { if (s.collapsible) state[s.title] = true; });\n    setExpanded(state);\n  }, []);\n\n  // Gestisce l'espansione/collasso delle sezioni\n  const toggleExpand = (title) => {\n    setExpanded(prev => ({ ...prev, [title]: !prev[title] }));\n  };\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    const extra = name === 'metratura_reale' ? { metriTeorici: parseFloat(formData.metri_teorici || 0) } : {};\n    const result = validateField(name, value, extra);\n\n    // Assicurati che gli errori siano sempre stringhe o null\n    const errorMessage = result.valid ? null :\n      (typeof result.message === 'object' ?\n        (result.message?.message || JSON.stringify(result.message)) :\n        result.message);\n\n    setFormErrors(prev => ({ ...prev, [name]: errorMessage }));\n\n    setWarnings(prev => {\n      // Assicurati che i warning siano sempre stringhe o null\n      const warningMessage = result.warning ?\n        (typeof result.message === 'object' ?\n          (result.message?.message || JSON.stringify(result.message)) :\n          result.message) :\n        null;\n\n      const newWarnings = { ...prev, [name]: warningMessage };\n      // Mostra il banner di avviso se ci sono warning\n      setShowWarnings(Object.values(newWarnings).some(w => w));\n      return newWarnings;\n    });\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n\n    if (!validation.isValid) {\n      // Assicurati che gli errori siano sempre stringhe o null\n      const processedErrors = {};\n      Object.entries(validation.errors).forEach(([key, value]) => {\n        processedErrors[key] = typeof value === 'object' ?\n          (value?.message || JSON.stringify(value)) : value;\n      });\n\n      // Assicurati che i warning siano sempre stringhe o null\n      const processedWarnings = {};\n      Object.entries(validation.warnings).forEach(([key, value]) => {\n        processedWarnings[key] = typeof value === 'object' ?\n          (value?.message || JSON.stringify(value)) : value;\n      });\n\n      setFormErrors(processedErrors);\n      setWarnings(processedWarnings);\n      setShowWarnings(Object.values(processedWarnings).some(w => w));\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n\n    try {\n      const finalData = { \n        ...validation.validatedData, \n        id_cavo: validation.validatedData.id_cavo.toUpperCase(),\n        metratura_reale: '0' // Set default value\n      };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n\n  // Gestisce l'annullamento del form\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    } else {\n      redirectToVisualizzaCavi(navigate);\n    }\n  };\n\n  // Renderizza un singolo campo del form\n  const renderField = (field) => {\n    // Gestisce i valori null/undefined per evitare la visualizzazione di \"null\"\n    const fieldValue = formData[field.name] ?? '';\n\n    // Gestisce i messaggi di errore e warning in modo più pulito\n    const errorMessage = formErrors[field.name];\n    const warningMessage = warnings[field.name];\n\n    const helperText = errorMessage || warningMessage || '';\n\n    return (\n      <TextField\n        select={field.type === 'select'}\n        fullWidth\n        size=\"small\"\n        margin=\"none\"\n        label={field.label}\n        name={field.name}\n        value={fieldValue}\n        onChange={handleChange}\n        error={!!errorMessage}\n        helperText={helperText}\n        required={field.required}\n        variant=\"outlined\"\n        InputLabelProps={{\n          shrink: true,\n          sx: {\n            fontWeight: field.required ? 600 : 400,\n            color: field.required ? 'primary.main' : 'text.secondary'\n          }\n        }}\n        sx={{\n          '& .MuiOutlinedInput-root': {\n            '&:hover fieldset': {\n              borderColor: 'primary.main',\n            },\n            '&.Mui-focused fieldset': {\n              borderWidth: 2,\n            },\n          },\n          '& .MuiFormHelperText-root': {\n            fontSize: '0.75rem',\n            marginTop: '4px',\n            color: errorMessage ? 'error.main' : warningMessage ? 'warning.main' : 'text.secondary'\n          }\n        }}\n        {...(field.inputProps || {})}\n      >\n        {field.options?.map(opt => (\n          <MenuItem key={opt} value={opt}>{opt}</MenuItem>\n        ))}\n      </TextField>\n    );\n  };\n\n  return (\n    <Paper\n      elevation={2}\n      sx={{\n        border: '1px solid #e0e0e0',\n        borderRadius: 3,\n        overflow: 'hidden',\n        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',\n        boxShadow: '0 4px 20px rgba(0,0,0,0.08)'\n      }}\n    >\n      <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n        {/* Avviso di validazione */}\n        {showWarnings && (\n          <Alert\n            severity=\"warning\"\n            icon={<WarningIcon />}\n            sx={{\n              borderRadius: 0,\n              py: 0.5\n            }}\n          >\n            Alcuni campi potrebbero necessitare revisione\n          </Alert>\n        )}\n\n        {/* Sezioni del form */}\n        {SECTION_CONFIG.map((section, index) => {\n          const isExpanded = section.collapsible ? expanded[section.title] : true;\n\n          return (\n            <Box key={section.title} sx={{ borderBottom: index < SECTION_CONFIG.length - 1 ? '0.5px solid #e0e0e0' : 'none' }}>\n              {/* Intestazione della sezione */}\n              <Box\n                sx={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  px: 2,\n                  py: 0.8,\n                  bgcolor: '#f5f5f5',\n                  borderBottom: isExpanded ? '0.5px solid #e0e0e0' : 'none',\n                  cursor: section.collapsible ? 'pointer' : 'default',\n                }}\n                onClick={() => section.collapsible && toggleExpand(section.title)}\n              >\n                <Typography\n                  variant=\"subtitle1\"\n                  sx={{\n                    fontWeight: 600,\n                    color: '#333',\n                    fontSize: '0.9rem'\n                  }}\n                >\n                  {section.title}\n                </Typography>\n\n                {section.collapsible && (\n                  <IconButton\n                    size=\"small\"\n                    sx={{ ml: 1 }}\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      toggleExpand(section.title);\n                    }}\n                  >\n                    {isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}\n                  </IconButton>\n                )}\n              </Box>\n\n              {/* Contenuto della sezione */}\n              {isExpanded && (\n                <Box sx={{ p: 1.2 }}>\n                  <Grid container spacing={1}>\n                    {section.fields.map(field => (\n                      <Grid \n                        item \n                        xs={field.gridSize?.xs || 12} \n                        sm={field.gridSize?.sm || 6} \n                        md={field.gridSize?.md || 4} \n                        key={field.name}\n                      >\n                        {renderField(field)}\n                      </Grid>\n                    ))}\n                  </Grid>\n                </Box>\n              )}\n            </Box>\n          );\n        })}\n\n        {/* Pulsanti di azione */}\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: 1.5,\n            p: 1.2,\n            bgcolor: '#f9f9f9',\n            borderTop: '0.5px solid #e0e0e0'\n          }}\n        >\n          <Tooltip title=\"Annulla e torna indietro\">\n            <Button\n              variant=\"outlined\"\n              color=\"inherit\"\n              onClick={handleCancel}\n              disabled={loading}\n              startIcon={<CancelIcon />}\n              size=\"small\"\n            >\n              Annulla\n            </Button>\n          </Tooltip>\n\n          <Tooltip title={mode === 'add' ? 'Salva nuovo cavo' : 'Salva modifiche al cavo'}>\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={loading ? <CircularProgress size={16} color=\"inherit\" /> : <SaveIcon />}\n              disabled={loading}\n              size=\"small\"\n            >\n              {loading ? 'Salvataggio...' : mode === 'add' ? 'Salva' : 'Salva'}\n            </Button>\n          </Tooltip>\n        </Box>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default CavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,qBAAqB,EAC1CC,eAAe,IAAIC,mBAAmB,QACjC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,6BAA6B;AAC7E,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,cAAc,GAAG,CACrB;EACEC,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;MAAEC,KAAK,EAAE;QAAEC,aAAa,EAAE;MAAY;IAAE;EAAE,CAAC,EAC5G;IAAEL,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC;AAEzC,CAAC,EACD;EACEJ,KAAK,EAAE,0BAA0B;EACjCC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC7C;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC;EACzC;EACA;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAa;EACvC;EAAA;AAEJ,CAAC,EACD;EACEJ,KAAK,EAAE,UAAU;EACjBC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE,qBAAqB;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EACjG;IAAET,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,iBAAiB;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EACzF;IAAET,IAAI,EAAE,6BAA6B;IAAEC,KAAK,EAAE,6BAA6B;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC;AAErH,CAAC,EACD;EACEZ,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,mBAAmB;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EAC7F;IAAET,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EACrF;IAAET,IAAI,EAAE,2BAA2B;IAAEC,KAAK,EAAE,2BAA2B;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC;AAEjH,CAAC,EACD;EACEZ,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,QAAQ,EAAE;EAAK,CAAC;AAErE,CAAC,CACF;AAED,MAAMQ,WAAW,GAAG;EAClBC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,EAAE;EACXC,EAAE,EAAE,GAAG;EACPC,mBAAmB,EAAE,EAAE;EACvBC,eAAe,EAAE,EAAE;EACnBC,2BAA2B,EAAE,EAAE;EAC/BC,iBAAiB,EAAE,EAAE;EACrBC,aAAa,EAAE,EAAE;EACjBC,yBAAyB,EAAE,EAAE;EAC7BC,aAAa,EAAE,EAAE;EACjBC,eAAe,EAAE;AACnB,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI,GAAG,KAAK;EAAEC,WAAW,GAAG,CAAC,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/F,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAGxE,QAAQ,CAAC;IAAE,GAAG6C,WAAW;IAAE,GAAGmB;EAAY,CAAC,CAAC;EAC5E,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC6E,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACiF,YAAY,EAAEC,eAAe,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkF,KAAK,GAAG,CAAC,CAAC;IAChBpD,cAAc,CAACqD,OAAO,CAACC,CAAC,IAAI;MAAE,IAAIA,CAAC,CAACpD,WAAW,EAAEkD,KAAK,CAACE,CAAC,CAACrD,KAAK,CAAC,GAAG,IAAI;IAAE,CAAC,CAAC;IAC1EgD,WAAW,CAACG,KAAK,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,YAAY,GAAItD,KAAK,IAAK;IAC9BgD,WAAW,CAACO,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACvD,KAAK,GAAG,CAACuD,IAAI,CAACvD,KAAK;IAAE,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMwD,YAAY,GAAIC,CAAC,IAAK;IAAA,IAAAC,eAAA;IAC1B,MAAM;MAAEvD,IAAI;MAAEwD;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCpB,WAAW,CAACe,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACpD,IAAI,GAAGwD;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,MAAME,KAAK,GAAG1D,IAAI,KAAK,iBAAiB,GAAG;MAAE2D,YAAY,EAAEC,UAAU,CAACxB,QAAQ,CAACX,aAAa,IAAI,CAAC;IAAE,CAAC,GAAG,CAAC,CAAC;IACzG,MAAMoC,MAAM,GAAGrE,aAAa,CAACQ,IAAI,EAAEwD,KAAK,EAAEE,KAAK,CAAC;;IAEhD;IACA,MAAMI,YAAY,GAAGD,MAAM,CAACE,KAAK,GAAG,IAAI,GACrC,OAAOF,MAAM,CAACG,OAAO,KAAK,QAAQ,GAChC,EAAAT,eAAA,GAAAM,MAAM,CAACG,OAAO,cAAAT,eAAA,uBAAdA,eAAA,CAAgBS,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,OAAO,CAAC,GAC1DH,MAAM,CAACG,OAAQ;IAEnBzB,aAAa,CAACa,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACpD,IAAI,GAAG8D;IAAa,CAAC,CAAC,CAAC;IAE1DrB,WAAW,CAACW,IAAI,IAAI;MAAA,IAAAe,gBAAA;MAClB;MACA,MAAMC,cAAc,GAAGP,MAAM,CAACQ,OAAO,GAClC,OAAOR,MAAM,CAACG,OAAO,KAAK,QAAQ,GAChC,EAAAG,gBAAA,GAAAN,MAAM,CAACG,OAAO,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBH,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,OAAO,CAAC,GAC1DH,MAAM,CAACG,OAAO,GAChB,IAAI;MAEN,MAAMM,WAAW,GAAG;QAAE,GAAGlB,IAAI;QAAE,CAACpD,IAAI,GAAGoE;MAAe,CAAC;MACvD;MACArB,eAAe,CAACwB,MAAM,CAACC,MAAM,CAACF,WAAW,CAAC,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC;MACxD,OAAOJ,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMK,YAAY,GAAG,MAAOrB,CAAC,IAAK;IAChCA,CAAC,CAACsB,cAAc,CAAC,CAAC;IAClBjC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMkC,UAAU,GAAGtF,gBAAgB,CAAC6C,QAAQ,CAAC;IAE7C,IAAI,CAACyC,UAAU,CAACC,OAAO,EAAE;MACvB;MACA,MAAMC,eAAe,GAAG,CAAC,CAAC;MAC1BR,MAAM,CAACS,OAAO,CAACH,UAAU,CAACI,MAAM,CAAC,CAAChC,OAAO,CAAC,CAAC,CAACiC,GAAG,EAAE1B,KAAK,CAAC,KAAK;QAC1DuB,eAAe,CAACG,GAAG,CAAC,GAAG,OAAO1B,KAAK,KAAK,QAAQ,GAC7C,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACV,KAAK,CAAC,GAAIA,KAAK;MACrD,CAAC,CAAC;;MAEF;MACA,MAAM2B,iBAAiB,GAAG,CAAC,CAAC;MAC5BZ,MAAM,CAACS,OAAO,CAACH,UAAU,CAACrC,QAAQ,CAAC,CAACS,OAAO,CAAC,CAAC,CAACiC,GAAG,EAAE1B,KAAK,CAAC,KAAK;QAC5D2B,iBAAiB,CAACD,GAAG,CAAC,GAAG,OAAO1B,KAAK,KAAK,QAAQ,GAC/C,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACV,KAAK,CAAC,GAAIA,KAAK;MACrD,CAAC,CAAC;MAEFjB,aAAa,CAACwC,eAAe,CAAC;MAC9BtC,WAAW,CAAC0C,iBAAiB,CAAC;MAC9BpC,eAAe,CAACwB,MAAM,CAACC,MAAM,CAACW,iBAAiB,CAAC,CAACV,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC;MAC9D/B,UAAU,CAAC,KAAK,CAAC;MACjBX,OAAO,CAAC,0BAA0B,CAAC;MACnC;IACF;IAEA,IAAI;MACF,MAAMoD,SAAS,GAAG;QAChB,GAAGP,UAAU,CAACQ,aAAa;QAC3B1E,OAAO,EAAEkE,UAAU,CAACQ,aAAa,CAAC1E,OAAO,CAAC2E,WAAW,CAAC,CAAC;QACvD5D,eAAe,EAAE,GAAG,CAAC;MACvB,CAAC;MACD,MAAMI,QAAQ,CAACsD,SAAS,CAAC;MACzBzC,UAAU,CAAC,KAAK,CAAC;MACjBZ,SAAS,CAAC,QAAQH,IAAI,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY,gBAAgB,CAAC;MAC7EnC,wBAAwB,CAAC0C,QAAQ,CAAC;IACpC,CAAC,CAAC,OAAOoD,GAAG,EAAE;MACZ5C,UAAU,CAAC,KAAK,CAAC;MACjBX,OAAO,CAACuD,GAAG,CAACvB,OAAO,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMwB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvD,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLxC,wBAAwB,CAAC0C,QAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMsD,WAAW,GAAIC,KAAK,IAAK;IAAA,IAAAC,oBAAA,EAAAC,cAAA;IAC7B;IACA,MAAMC,UAAU,IAAAF,oBAAA,GAAGvD,QAAQ,CAACsD,KAAK,CAAC1F,IAAI,CAAC,cAAA2F,oBAAA,cAAAA,oBAAA,GAAI,EAAE;;IAE7C;IACA,MAAM7B,YAAY,GAAGxB,UAAU,CAACoD,KAAK,CAAC1F,IAAI,CAAC;IAC3C,MAAMoE,cAAc,GAAG5B,QAAQ,CAACkD,KAAK,CAAC1F,IAAI,CAAC;IAE3C,MAAM8F,UAAU,GAAGhC,YAAY,IAAIM,cAAc,IAAI,EAAE;IAEvD,oBACEzE,OAAA,CAAC3B,SAAS;MACR+H,MAAM,EAAEL,KAAK,CAACM,IAAI,KAAK,QAAS;MAChCC,SAAS;MACTC,IAAI,EAAC,OAAO;MACZC,MAAM,EAAC,MAAM;MACblG,KAAK,EAAEyF,KAAK,CAACzF,KAAM;MACnBD,IAAI,EAAE0F,KAAK,CAAC1F,IAAK;MACjBwD,KAAK,EAAEqC,UAAW;MAClBO,QAAQ,EAAE/C,YAAa;MACvBgD,KAAK,EAAE,CAAC,CAACvC,YAAa;MACtBgC,UAAU,EAAEA,UAAW;MACvB5F,QAAQ,EAAEwF,KAAK,CAACxF,QAAS;MACzBoG,OAAO,EAAC,UAAU;MAClBC,eAAe,EAAE;QACfC,MAAM,EAAE,IAAI;QACZC,EAAE,EAAE;UACFC,UAAU,EAAEhB,KAAK,CAACxF,QAAQ,GAAG,GAAG,GAAG,GAAG;UACtCyG,KAAK,EAAEjB,KAAK,CAACxF,QAAQ,GAAG,cAAc,GAAG;QAC3C;MACF,CAAE;MACFuG,EAAE,EAAE;QACF,0BAA0B,EAAE;UAC1B,kBAAkB,EAAE;YAClBG,WAAW,EAAE;UACf,CAAC;UACD,wBAAwB,EAAE;YACxBC,WAAW,EAAE;UACf;QACF,CAAC;QACD,2BAA2B,EAAE;UAC3BC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE,KAAK;UAChBJ,KAAK,EAAE7C,YAAY,GAAG,YAAY,GAAGM,cAAc,GAAG,cAAc,GAAG;QACzE;MACF,CAAE;MAAA,IACGsB,KAAK,CAACvF,UAAU,IAAI,CAAC,CAAC;MAAA6G,QAAA,GAAApB,cAAA,GAE1BF,KAAK,CAACuB,OAAO,cAAArB,cAAA,uBAAbA,cAAA,CAAesB,GAAG,CAACC,GAAG,iBACrBxH,OAAA,CAACxB,QAAQ;QAAWqF,KAAK,EAAE2D,GAAI;QAAAH,QAAA,EAAEG;MAAG,GAArBA,GAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA6B,CAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEhB,CAAC;EAED,oBACE5H,OAAA,CAACnB,KAAK;IACJgJ,SAAS,EAAE,CAAE;IACbf,EAAE,EAAE;MACFgB,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,SAAS,EAAE;IACb,CAAE;IAAAb,QAAA,eAEFrH,OAAA,CAAC5B,GAAG;MAAC+J,SAAS,EAAC,MAAM;MAAChG,QAAQ,EAAE6C,YAAa;MAACoD,UAAU;MAAAf,QAAA,GAErDlE,YAAY,iBACXnD,OAAA,CAACvB,KAAK;QACJ4J,QAAQ,EAAC,SAAS;QAClBC,IAAI,eAAEtI,OAAA,CAACZ,WAAW;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBd,EAAE,EAAE;UACFiB,YAAY,EAAE,CAAC;UACfQ,EAAE,EAAE;QACN,CAAE;QAAAlB,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAGA3H,cAAc,CAACsH,GAAG,CAAC,CAACiB,OAAO,EAAEC,KAAK,KAAK;QACtC,MAAMC,UAAU,GAAGF,OAAO,CAACrI,WAAW,GAAG8C,QAAQ,CAACuF,OAAO,CAACtI,KAAK,CAAC,GAAG,IAAI;QAEvE,oBACEF,OAAA,CAAC5B,GAAG;UAAqB0I,EAAE,EAAE;YAAE6B,YAAY,EAAEF,KAAK,GAAGxI,cAAc,CAAC2I,MAAM,GAAG,CAAC,GAAG,qBAAqB,GAAG;UAAO,CAAE;UAAAvB,QAAA,gBAEhHrH,OAAA,CAAC5B,GAAG;YACF0I,EAAE,EAAE;cACF+B,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpBC,EAAE,EAAE,CAAC;cACLT,EAAE,EAAE,GAAG;cACPU,OAAO,EAAE,SAAS;cAClBN,YAAY,EAAED,UAAU,GAAG,qBAAqB,GAAG,MAAM;cACzDQ,MAAM,EAAEV,OAAO,CAACrI,WAAW,GAAG,SAAS,GAAG;YAC5C,CAAE;YACFgJ,OAAO,EAAEA,CAAA,KAAMX,OAAO,CAACrI,WAAW,IAAIqD,YAAY,CAACgF,OAAO,CAACtI,KAAK,CAAE;YAAAmH,QAAA,gBAElErH,OAAA,CAACrB,UAAU;cACTgI,OAAO,EAAC,WAAW;cACnBG,EAAE,EAAE;gBACFC,UAAU,EAAE,GAAG;gBACfC,KAAK,EAAE,MAAM;gBACbG,QAAQ,EAAE;cACZ,CAAE;cAAAE,QAAA,EAEDmB,OAAO,CAACtI;YAAK;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEZY,OAAO,CAACrI,WAAW,iBAClBH,OAAA,CAAClB,UAAU;cACTyH,IAAI,EAAC,OAAO;cACZO,EAAE,EAAE;gBAAEsC,EAAE,EAAE;cAAE,CAAE;cACdD,OAAO,EAAGxF,CAAC,IAAK;gBACdA,CAAC,CAAC0F,eAAe,CAAC,CAAC;gBACnB7F,YAAY,CAACgF,OAAO,CAACtI,KAAK,CAAC;cAC7B,CAAE;cAAAmH,QAAA,EAEDqB,UAAU,gBAAG1I,OAAA,CAACN,mBAAmB;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5H,OAAA,CAACR,qBAAqB;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLc,UAAU,iBACT1I,OAAA,CAAC5B,GAAG;YAAC0I,EAAE,EAAE;cAAEwC,CAAC,EAAE;YAAI,CAAE;YAAAjC,QAAA,eAClBrH,OAAA,CAAChB,IAAI;cAACuK,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnC,QAAA,EACxBmB,OAAO,CAACpI,MAAM,CAACmH,GAAG,CAACxB,KAAK;gBAAA,IAAA0D,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;gBAAA,oBACvB3J,OAAA,CAAChB,IAAI;kBACH4K,IAAI;kBACJhJ,EAAE,EAAE,EAAA6I,eAAA,GAAA1D,KAAK,CAACpF,QAAQ,cAAA8I,eAAA,uBAAdA,eAAA,CAAgB7I,EAAE,KAAI,EAAG;kBAC7BC,EAAE,EAAE,EAAA6I,gBAAA,GAAA3D,KAAK,CAACpF,QAAQ,cAAA+I,gBAAA,uBAAdA,gBAAA,CAAgB7I,EAAE,KAAI,CAAE;kBAC5BC,EAAE,EAAE,EAAA6I,gBAAA,GAAA5D,KAAK,CAACpF,QAAQ,cAAAgJ,gBAAA,uBAAdA,gBAAA,CAAgB7I,EAAE,KAAI,CAAE;kBAAAuG,QAAA,EAG3BvB,WAAW,CAACC,KAAK;gBAAC,GAFdA,KAAK,CAAC1F,IAAI;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGX,CAAC;cAAA,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA,GAzDOY,OAAO,CAACtI,KAAK;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0DlB,CAAC;MAEV,CAAC,CAAC,eAGF5H,OAAA,CAAC5B,GAAG;QACF0I,EAAE,EAAE;UACF+B,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,UAAU;UAC1Be,GAAG,EAAE,GAAG;UACRP,CAAC,EAAE,GAAG;UACNL,OAAO,EAAE,SAAS;UAClBa,SAAS,EAAE;QACb,CAAE;QAAAzC,QAAA,gBAEFrH,OAAA,CAACjB,OAAO;UAACmB,KAAK,EAAC,0BAA0B;UAAAmH,QAAA,eACvCrH,OAAA,CAAC1B,MAAM;YACLqI,OAAO,EAAC,UAAU;YAClBK,KAAK,EAAC,SAAS;YACfmC,OAAO,EAAEtD,YAAa;YACtBkE,QAAQ,EAAEhH,OAAQ;YAClBiH,SAAS,eAAEhK,OAAA,CAACV,UAAU;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BrB,IAAI,EAAC,OAAO;YAAAc,QAAA,EACb;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEV5H,OAAA,CAACjB,OAAO;UAACmB,KAAK,EAAE+B,IAAI,KAAK,KAAK,GAAG,kBAAkB,GAAG,yBAA0B;UAAAoF,QAAA,eAC9ErH,OAAA,CAAC1B,MAAM;YACL+H,IAAI,EAAC,QAAQ;YACbM,OAAO,EAAC,WAAW;YACnBK,KAAK,EAAC,SAAS;YACfgD,SAAS,EAAEjH,OAAO,gBAAG/C,OAAA,CAACtB,gBAAgB;cAAC6H,IAAI,EAAE,EAAG;cAACS,KAAK,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5H,OAAA,CAACd,QAAQ;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnFmC,QAAQ,EAAEhH,OAAQ;YAClBwD,IAAI,EAAC,OAAO;YAAAc,QAAA,EAEXtE,OAAO,GAAG,gBAAgB,GAAGd,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG;UAAO;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACrF,EAAA,CAxSIP,QAAQ;EAAA,QACKrC,WAAW;AAAA;AAAAsK,EAAA,GADxBjI,QAAQ;AA0Sd,eAAeA,QAAQ;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}