#!/usr/bin/env python3
"""
Script per testare la connessione al database PostgreSQL.
Verifica che il database sia configurato correttamente e accessibile.
"""

import sys
import os
import psycopg2
from psycopg2.extras import RealDictCursor
from pathlib import Path

# Aggiungi il percorso del backend al PYTHONPATH
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from backend.config import settings
    print("✅ Configurazione caricata con successo")
    print(f"Database URL: {settings.DATABASE_URL}")
except ImportError as e:
    print(f"❌ Errore nell'importazione della configurazione: {e}")
    sys.exit(1)

def test_database_connection():
    """Testa la connessione al database PostgreSQL."""
    print("\n=== Test connessione database PostgreSQL ===")
    
    try:
        # Test connessione diretta con psycopg2
        print(f"Tentativo di connessione a: {settings.DB_HOST}:{settings.DB_PORT}")
        print(f"Database: {settings.DB_NAME}")
        print(f"Utente: {settings.DB_USER}")
        
        conn = psycopg2.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            dbname=settings.DB_NAME,
            user=settings.DB_USER,
            password=settings.DB_PASSWORD,
            cursor_factory=RealDictCursor
        )
        
        print("✅ Connessione al database riuscita!")
        
        # Test query semplice
        with conn.cursor() as cur:
            cur.execute("SELECT version();")
            version = cur.fetchone()
            print(f"✅ Versione PostgreSQL: {version['version']}")
            
            # Verifica esistenza tabelle principali
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """)
            tables = cur.fetchall()
            
            if tables:
                print("✅ Tabelle esistenti nel database:")
                for table in tables:
                    print(f"  - {table['table_name']}")
            else:
                print("⚠️  Nessuna tabella trovata nel database")
                
        conn.close()
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ Errore di connessione al database: {e}")
        print("\nPossibili soluzioni:")
        print("1. Verifica che PostgreSQL sia in esecuzione")
        print("2. Controlla le credenziali del database")
        print("3. Verifica che il database 'cantieri' esista")
        print("4. Controlla le impostazioni del firewall")
        return False
        
    except Exception as e:
        print(f"❌ Errore generico: {e}")
        return False

def test_sqlalchemy_connection():
    """Testa la connessione tramite SQLAlchemy."""
    print("\n=== Test connessione SQLAlchemy ===")
    
    try:
        from backend.database import engine, get_db
        from sqlalchemy import text
        
        # Test connessione engine
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            
            if test_value == 1:
                print("✅ Connessione SQLAlchemy riuscita!")
                return True
            else:
                print("❌ Test SQLAlchemy fallito")
                return False
                
    except Exception as e:
        print(f"❌ Errore SQLAlchemy: {e}")
        return False

def create_database_if_not_exists():
    """Crea il database se non esiste."""
    print("\n=== Verifica/Creazione database ===")
    
    try:
        # Connessione al database postgres (default) per creare il database cantieri
        conn = psycopg2.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            dbname='postgres',  # Database di default
            user=settings.DB_USER,
            password=settings.DB_PASSWORD
        )
        conn.autocommit = True
        
        with conn.cursor() as cur:
            # Verifica se il database esiste
            cur.execute("""
                SELECT 1 FROM pg_database WHERE datname = %s
            """, (settings.DB_NAME,))
            
            if cur.fetchone():
                print(f"✅ Database '{settings.DB_NAME}' già esistente")
            else:
                print(f"⚠️  Database '{settings.DB_NAME}' non trovato. Creazione in corso...")
                cur.execute(f'CREATE DATABASE "{settings.DB_NAME}"')
                print(f"✅ Database '{settings.DB_NAME}' creato con successo!")
                
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la creazione del database: {e}")
        return False

def main():
    """Funzione principale."""
    print("🔍 Test di connessione al database CMS")
    print("=" * 50)
    
    # Test 1: Creazione database se necessario
    if not create_database_if_not_exists():
        print("❌ Impossibile creare/verificare il database")
        return False
    
    # Test 2: Connessione diretta
    if not test_database_connection():
        print("❌ Test di connessione fallito")
        return False
    
    # Test 3: Connessione SQLAlchemy
    if not test_sqlalchemy_connection():
        print("❌ Test SQLAlchemy fallito")
        return False
    
    print("\n🎉 Tutti i test di connessione sono passati!")
    print("Il database è configurato correttamente e accessibile.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
