import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Box, Typography, Grid, Paper } from '@mui/material';

const COLORS = {
  primary: '#2c3e50',
  secondary: '#34495e',
  success: '#3498db',
  warning: '#5d6d7e',
  info: '#85929e',
  error: '#566573',
  light: '#ecf0f1',
  dark: '#2c3e50',
  accent: '#7fb3d3'
};

const ProgressChart = ({ data }) => {
  if (!data) return null;

  // Dati per il grafico a torta dell'avanzamento
  const progressData = [
    {
      name: 'Metri Posati',
      value: data.metri_posati,
      color: COLORS.success
    },
    {
      name: 'Metri Rimanenti',
      value: data.metri_da_posare,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a torta dei cavi
  const caviData = [
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_posati,
      color: COLORS.success
    },
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_rimanenti,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a barre delle metriche principali
  const metricsData = [
    {
      name: 'Metri',
      Totali: data.metri_totali,
      Posati: data.metri_posati,
      Rimanenti: data.metri_da_posare
    },
    {
      name: 'Cavi',
      Totali: data.totale_cavi,
      Posati: data.cavi_posati,
      Rimanenti: data.cavi_rimanenti
    }
  ];

  // Dati per il grafico temporale della posa recente
  const posaTrendData = data.posa_recente?.map(posa => ({
    data: posa.data,
    metri: posa.metri
  })) || [];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Paper sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
      <Grid container spacing={2}>
        {/* Grafici di Distribuzione */}
        <Grid item xs={12} md={6}>
          <Box sx={{
            border: '1px solid #e0e0e0',
            borderRadius: 1,
            overflow: 'hidden',
            height: '300px'
          }}>
            <Box sx={{
              bgcolor: '#f8f9fa',
              p: 1.5,
              borderBottom: '1px solid #e0e0e0'
            }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                Distribuzione Metri
              </Typography>
            </Box>
            <Box sx={{ p: 2, height: 'calc(100% - 50px)', display: 'flex', alignItems: 'center' }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={progressData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                    stroke="none"
                  >
                    {progressData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <Box sx={{
                            bgcolor: 'white',
                            p: 1,
                            border: '1px solid #e0e0e0',
                            borderRadius: 1,
                            fontSize: '12px'
                          }}>
                            <Typography variant="caption" sx={{ fontWeight: 600 }}>
                              {payload[0].name}: {payload[0].value}m
                            </Typography>
                          </Box>
                        );
                      }
                      return null;
                    }}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Box>
        </Grid>

        {/* Grafico Cavi */}
        <Grid item xs={12} md={6}>
          <Box sx={{
            border: '1px solid #e0e0e0',
            borderRadius: 1,
            overflow: 'hidden',
            height: '300px'
          }}>
            <Box sx={{
              bgcolor: '#f8f9fa',
              p: 1.5,
              borderBottom: '1px solid #e0e0e0'
            }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                Distribuzione Cavi
              </Typography>
            </Box>
            <Box sx={{ p: 2, height: 'calc(100% - 50px)', display: 'flex', alignItems: 'center' }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={caviData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                    stroke="none"
                  >
                    {caviData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <Box sx={{
                            bgcolor: 'white',
                            p: 1,
                            border: '1px solid #e0e0e0',
                            borderRadius: 1,
                            fontSize: '12px'
                          }}>
                            <Typography variant="caption" sx={{ fontWeight: 600 }}>
                              {payload[0].name}: {payload[0].value}
                            </Typography>
                          </Box>
                        );
                      }
                      return null;
                    }}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Box>
        </Grid>

        {/* Performance e Trend (solo se ci sono dati) */}
        {data.media_giornaliera && (
          <Grid item xs={12}>
            <Box sx={{
              border: '1px solid #e0e0e0',
              borderRadius: 1,
              overflow: 'hidden'
            }}>
              <Box sx={{
                bgcolor: '#f8f9fa',
                p: 1.5,
                borderBottom: '1px solid #e0e0e0'
              }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  Performance e Previsioni
                </Typography>
              </Box>
              <Box sx={{ p: 2 }}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.primary }}>
                        {data.media_giornaliera}m
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#666' }}>
                        Media Giornaliera
                      </Typography>
                    </Box>
                  </Grid>
                  {data.giorni_stimati && (
                    <>
                      <Grid item xs={12} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h6" sx={{ fontWeight: 600, color: COLORS.warning }}>
                            {data.giorni_stimati}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666' }}>
                            Giorni Stimati
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="body2" sx={{ fontWeight: 600, color: COLORS.info }}>
                            {data.data_completamento}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#666' }}>
                            Data Completamento Prevista
                          </Typography>
                        </Box>
                      </Grid>
                    </>
                  )}
                </Grid>
              </Box>
            </Box>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};

export default ProgressChart;
