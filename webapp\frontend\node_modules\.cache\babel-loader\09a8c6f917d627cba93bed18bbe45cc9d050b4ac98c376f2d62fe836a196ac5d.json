{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\UserPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, Snackbar, Alert, IconButton, ToggleButton, ToggleButtonGroup } from '@mui/material';\nimport { Construction as ConstructionIcon, Delete as DeleteIcon, Add as AddIcon, ContentCopy as ContentCopyIcon, Info as InfoIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon } from '@mui/icons-material';\nimport CantieriFilterableTable from '../components/cantieri/CantieriFilterableTable';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserPage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser,\n    selectCantiere\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'cards'\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo con la password\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo!\\nCodice univoco: ${createdCantiere.codice_univoco}\\nPassword: ${newCantiereData.password_cantiere}\\n\\nSalva queste informazioni in un luogo sicuro!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = cantiere => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage\n    selectCantiere(cantiere);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (event, newViewMode) => {\n    if (newViewMode !== null) {\n      setViewMode(newViewMode);\n    }\n  };\n\n  // Gestisce la copia del codice univoco\n  const handleCopyCode = codiceUnivoco => {\n    navigator.clipboard.writeText(codiceUnivoco);\n    setNotification({\n      open: true,\n      message: 'Codice univoco copiato negli appunti',\n      severity: 'success'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cantieri-page\",\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: isImpersonating && impersonatedUser ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}` : \"Visualizza e gestisci i tuoi cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        className: \"primary-button\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 22\n        }, this),\n        onClick: handleOpenCreateDialog,\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cantieri...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 9\n    }, this) : cantieri.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Nessun cantiere trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Crea un nuovo cantiere per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        className: \"primary-button\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 24\n        }, this),\n        onClick: handleOpenCreateDialog,\n        sx: {\n          mt: 2\n        },\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"cantiere-header\",\n              children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cantiere.nome\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Descrizione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this), \" \", cantiere.descrizione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mr: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this), \" ********\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  setNotification({\n                    open: true,\n                    message: 'Per motivi di sicurezza, la password è visibile solo al momento della creazione del cantiere.',\n                    severity: 'info'\n                  });\n                },\n                title: \"Informazioni sulla password\",\n                children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mr: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Codice Univoco:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this), \" \", cantiere.codice_univoco || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  navigator.clipboard.writeText(cantiere.codice_univoco);\n                  setNotification({\n                    open: true,\n                    message: 'Codice univoco copiato negli appunti',\n                    severity: 'success'\n                  });\n                },\n                title: \"Copia codice univoco\",\n                children: /*#__PURE__*/_jsxDEV(ContentCopyIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"primary-button\",\n              onClick: () => handleSelectCantiere(cantiere),\n              children: \"Gestione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"error-button\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenDeleteDialog(cantiere),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 15\n        }, this)\n      }, cantiere.id_cantiere, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openCreateDialog,\n      onClose: handleCloseCreateDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Crea Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Inserisci i dati per creare un nuovo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          name: \"nome\",\n          label: \"Nome Cantiere\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.nome,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"descrizione\",\n          label: \"Descrizione\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.descrizione,\n          onChange: handleInputChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"password_cantiere\",\n          label: \"Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.password_cantiere,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"conferma_password\",\n          label: \"Conferma Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.conferma_password,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseCreateDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCantiere,\n          variant: \"contained\",\n          className: \"primary-button\",\n          children: \"Crea\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDeleteDialog,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Elimina Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"ATTENZIONE: Sei sicuro di voler eliminare il cantiere \\\"\", selectedCantiere === null || selectedCantiere === void 0 ? void 0 : selectedCantiere.nome, \"\\\" e tutti i suoi dati? Questa operazione non pu\\xF2 essere annullata.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseDeleteDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCantiere,\n          variant: \"contained\",\n          className: \"error-button\",\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            whiteSpace: 'pre-line'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPage, \"CYLulL/gs4ZbB7FpRJbykF3PsQ8=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = UserPage;\nexport default UserPage;\nvar _c;\n$RefreshReg$(_c, \"UserPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "TextField", "Snackbar", "<PERSON><PERSON>", "IconButton", "ToggleButton", "ToggleButtonGroup", "Construction", "ConstructionIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "ContentCopy", "ContentCopyIcon", "Info", "InfoIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "CantieriFilterableTable", "useAuth", "useNavigate", "cantieriService", "jsxDEV", "_jsxDEV", "UserPage", "_s", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "selectCantiere", "navigate", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "openCreateDialog", "setOpenCreateDialog", "openDeleteDialog", "setOpenDeleteDialog", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "newCantiereData", "setNewCantiereData", "nome", "descrizione", "password_cantiere", "conferma_password", "notification", "setNotification", "open", "message", "severity", "viewMode", "setViewMode", "fetchCantieri", "data", "role", "getUserCantieri", "id", "getMyCantieri", "err", "console", "handleOpenCreateDialog", "handleCloseCreateDialog", "handleOpenDeleteDialog", "cantiere", "handleCloseDeleteDialog", "handleInputChange", "e", "name", "value", "target", "handleCreateCantiere", "createdCantiere", "createCantiere", "codice_univoco", "handleDeleteCantiere", "deleteCantiere", "id_cantiere", "filter", "c", "handleSelectCantiere", "log", "handleCloseNotification", "handleViewModeChange", "event", "newViewMode", "handleCopyCode", "codiceUnivoco", "navigator", "clipboard", "writeText", "className", "children", "variant", "gutterBottom", "username", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "display", "justifyContent", "alignItems", "startIcon", "onClick", "length", "p", "textAlign", "color", "mt", "container", "spacing", "map", "item", "xs", "sm", "md", "component", "mr", "size", "title", "fontSize", "onClose", "autoFocus", "margin", "label", "type", "fullWidth", "onChange", "required", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/UserPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  TextField,\n  Snackbar,\n  Alert,\n  IconButton,\n  ToggleButton,\n  ToggleButtonGroup\n} from '@mui/material';\nimport {\n  Construction as ConstructionIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon,\n  ContentCopy as ContentCopyIcon,\n  Info as InfoIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon\n} from '@mui/icons-material';\nimport CantieriFilterableTable from '../components/cantieri/CantieriFilterableTable';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\n\nconst UserPage = () => {\n  const { user, isImpersonating, impersonatedU<PERSON>, selectCantiere } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'cards'\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if (user?.role === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo con la password\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo!\\nCodice univoco: ${createdCantiere.codice_univoco}\\nPassword: ${newCantiereData.password_cantiere}\\n\\nSalva queste informazioni in un luogo sicuro!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = (cantiere) => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage\n    selectCantiere(cantiere);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (event, newViewMode) => {\n    if (newViewMode !== null) {\n      setViewMode(newViewMode);\n    }\n  };\n\n  // Gestisce la copia del codice univoco\n  const handleCopyCode = (codiceUnivoco) => {\n    navigator.clipboard.writeText(codiceUnivoco);\n    setNotification({\n      open: true,\n      message: 'Codice univoco copiato negli appunti',\n      severity: 'success'\n    });\n  };\n\n  return (\n    <Box className=\"cantieri-page\">\n      <Typography variant=\"h4\" gutterBottom>\n        {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"}\n      </Typography>\n\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"body1\">\n          {isImpersonating && impersonatedUser\n            ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}`\n            : \"Visualizza e gestisci i tuoi cantieri\"}\n        </Typography>\n        <Button\n          variant=\"contained\"\n          className=\"primary-button\"\n          startIcon={<AddIcon />}\n          onClick={handleOpenCreateDialog}\n        >\n          Nuovo Cantiere\n        </Button>\n      </Box>\n\n      {loading ? (\n        <Typography>Caricamento cantieri...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : cantieri.length === 0 ? (\n        <Paper sx={{ p: 3, textAlign: 'center' }}>\n          <Typography variant=\"h6\">Nessun cantiere trovato</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Crea un nuovo cantiere per iniziare\n          </Typography>\n          <Button\n            variant=\"contained\"\n            className=\"primary-button\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n            sx={{ mt: 2 }}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Paper>\n      ) : (\n        <Grid container spacing={3}>\n          {cantieri.map((cantiere) => (\n            <Grid item xs={12} sm={6} md={4} key={cantiere.id_cantiere}>\n              <Card>\n                <CardContent>\n                  <Box className=\"cantiere-header\">\n                    <ConstructionIcon />\n                    <Typography variant=\"h6\" component=\"div\">\n                      {cantiere.nome}\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    <strong>Descrizione:</strong> {cantiere.descrizione || 'N/A'}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1 }}>\n                      <strong>Password:</strong> ********\n                    </Typography>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => {\n                        setNotification({\n                          open: true,\n                          message: 'Per motivi di sicurezza, la password è visibile solo al momento della creazione del cantiere.',\n                          severity: 'info'\n                        });\n                      }}\n                      title=\"Informazioni sulla password\"\n                    >\n                      <InfoIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1 }}>\n                      <strong>Codice Univoco:</strong> {cantiere.codice_univoco || 'N/A'}\n                    </Typography>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => {\n                        navigator.clipboard.writeText(cantiere.codice_univoco);\n                        setNotification({\n                          open: true,\n                          message: 'Codice univoco copiato negli appunti',\n                          severity: 'success'\n                        });\n                      }}\n                      title=\"Copia codice univoco\"\n                    >\n                      <ContentCopyIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    variant=\"contained\"\n                    className=\"primary-button\"\n                    onClick={() => handleSelectCantiere(cantiere)}\n                  >\n                    Gestione Cavi\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    className=\"error-button\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleOpenDeleteDialog(cantiere)}\n                  >\n                    Elimina\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Dialog per creare un nuovo cantiere */}\n      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog}>\n        <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Inserisci i dati per creare un nuovo cantiere.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            name=\"nome\"\n            label=\"Nome Cantiere\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.nome}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"descrizione\"\n            label=\"Descrizione\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.descrizione}\n            onChange={handleInputChange}\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"password_cantiere\"\n            label=\"Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.password_cantiere}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"conferma_password\"\n            label=\"Conferma Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.conferma_password}\n            onChange={handleInputChange}\n            required\n          />\n\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseCreateDialog}>Annulla</Button>\n          <Button onClick={handleCreateCantiere} variant=\"contained\" className=\"primary-button\">\n            Crea\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per eliminare un cantiere */}\n      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>\n        <DialogTitle>Elimina Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            ATTENZIONE: Sei sicuro di voler eliminare il cantiere \"{selectedCantiere?.nome}\" e tutti i suoi dati?\n            Questa operazione non può essere annullata.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseDeleteDialog}>Annulla</Button>\n          <Button onClick={handleDeleteCantiere} variant=\"contained\" className=\"error-button\">\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          <div style={{ whiteSpace: 'pre-line' }}>{notification.message}</div>\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default UserPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,iBAAiB,QACZ,eAAe;AACtB,SACEC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,uBAAuB,MAAM,gDAAgD;AACpF,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC7E,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC;IACrD8D,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC;IAC/CoE,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwE,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFtB,UAAU,CAAC,IAAI,CAAC;QAChB,IAAIuB,IAAI;;QAER;QACA,IAAI,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,MAAK,OAAO,IAAI/B,eAAe,IAAIC,gBAAgB,EAAE;UACjE;UACA6B,IAAI,GAAG,MAAMpC,eAAe,CAACsC,eAAe,CAAC/B,gBAAgB,CAACgC,EAAE,CAAC;QACnE,CAAC,MAAM;UACL;UACAH,IAAI,GAAG,MAAMpC,eAAe,CAACwC,aAAa,CAAC,CAAC;QAC9C;QAEA7B,WAAW,CAACyB,IAAI,CAAC;MACnB,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAAC5B,KAAK,CAAC,sCAAsC,EAAE2B,GAAG,CAAC;QAC1D1B,QAAQ,CAAC,qDAAqD,CAAC;MACjE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDsB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC9B,IAAI,EAAEC,eAAe,EAAEC,gBAAgB,CAAC,CAAC;;EAE7C;EACA,MAAMoC,sBAAsB,GAAGA,CAAA,KAAM;IACnCpB,kBAAkB,CAAC;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFV,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM2B,uBAAuB,GAAGA,CAAA,KAAM;IACpC3B,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM4B,sBAAsB,GAAIC,QAAQ,IAAK;IAC3CzB,mBAAmB,CAACyB,QAAQ,CAAC;IAC7B3B,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM4B,uBAAuB,GAAGA,CAAA,KAAM;IACpC5B,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM2B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC7B,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAAC4B,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAAC/B,eAAe,CAACE,IAAI,IAAI,CAACF,eAAe,CAACI,iBAAiB,EAAE;MAC/DG,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAIV,eAAe,CAACI,iBAAiB,KAAKJ,eAAe,CAACK,iBAAiB,EAAE;MAC3EE,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,4BAA4B;QACrCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACF,MAAMsB,eAAe,GAAG,MAAMtD,eAAe,CAACuD,cAAc,CAAC;QAC3D/B,IAAI,EAAEF,eAAe,CAACE,IAAI;QAC1BC,WAAW,EAAEH,eAAe,CAACG,WAAW;QACxCC,iBAAiB,EAAEJ,eAAe,CAACI;MACrC,CAAC,CAAC;;MAEF;MACAf,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE4C,eAAe,CAAC,CAAC;;MAE3C;MACAV,uBAAuB,CAAC,CAAC;;MAEzB;MACAf,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYuB,eAAe,CAAC9B,IAAI,0CAA0C8B,eAAe,CAACE,cAAc,eAAelC,eAAe,CAACI,iBAAiB,mDAAmD;QACpNM,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAAC5B,KAAK,CAAC,sCAAsC,EAAE2B,GAAG,CAAC;MAC1DZ,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,qCAAqC;QAC9CC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMyB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACrC,gBAAgB,EAAE;IAEvB,IAAI;MACF,MAAMpB,eAAe,CAAC0D,cAAc,CAACtC,gBAAgB,CAACuC,WAAW,CAAC;;MAElE;MACAhD,WAAW,CAACD,QAAQ,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,WAAW,KAAKvC,gBAAgB,CAACuC,WAAW,CAAC,CAAC;;MAEjF;MACAZ,uBAAuB,CAAC,CAAC;;MAEzB;MACAlB,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYX,gBAAgB,CAACI,IAAI,0BAA0B;QACpEQ,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAAC5B,KAAK,CAAC,yCAAyC,EAAE2B,GAAG,CAAC;MAC7DZ,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM8B,oBAAoB,GAAIhB,QAAQ,IAAK;IACzCJ,OAAO,CAACqB,GAAG,CAAC,uBAAuB,EAAEjB,QAAQ,CAAC;;IAE9C;IACAtC,cAAc,CAACsC,QAAQ,CAAC;;IAExB;IACArC,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC;;EAED;EACA,MAAMuD,uBAAuB,GAAGA,CAAA,KAAM;IACpCnC,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IACnD,IAAIA,WAAW,KAAK,IAAI,EAAE;MACxBjC,WAAW,CAACiC,WAAW,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,aAAa,IAAK;IACxCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,aAAa,CAAC;IAC5CxC,eAAe,CAAC;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,sCAAsC;MAC/CC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACE9B,OAAA,CAACtC,GAAG;IAAC6G,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BxE,OAAA,CAACrC,UAAU;MAAC8G,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClCpE,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACsE,QAAQ,EAAE,GAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC,eAEb/E,OAAA,CAACtC,GAAG;MAACsH,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAZ,QAAA,gBACzFxE,OAAA,CAACrC,UAAU;QAAC8G,OAAO,EAAC,OAAO;QAAAD,QAAA,EACxBpE,eAAe,IAAIC,gBAAgB,GAChC,uCAAuCA,gBAAgB,CAACsE,QAAQ,EAAE,GAClE;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACb/E,OAAA,CAACnC,MAAM;QACL4G,OAAO,EAAC,WAAW;QACnBF,SAAS,EAAC,gBAAgB;QAC1Bc,SAAS,eAAErF,OAAA,CAACd,OAAO;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBO,OAAO,EAAE7C,sBAAuB;QAAA+B,QAAA,EACjC;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELrE,OAAO,gBACNV,OAAA,CAACrC,UAAU;MAAA6G,QAAA,EAAC;IAAuB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC9CnE,KAAK,gBACPZ,OAAA,CAACvB,KAAK;MAACqD,QAAQ,EAAC,OAAO;MAAA0C,QAAA,EAAE5D;IAAK;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrCvE,QAAQ,CAAC+E,MAAM,KAAK,CAAC,gBACvBvF,OAAA,CAACpC,KAAK;MAACoH,EAAE,EAAE;QAAEQ,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAjB,QAAA,gBACvCxE,OAAA,CAACrC,UAAU;QAAC8G,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7D/E,OAAA,CAACrC,UAAU;QAAC8G,OAAO,EAAC,OAAO;QAACiB,KAAK,EAAC,gBAAgB;QAAAlB,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/E,OAAA,CAACnC,MAAM;QACL4G,OAAO,EAAC,WAAW;QACnBF,SAAS,EAAC,gBAAgB;QAC1Bc,SAAS,eAAErF,OAAA,CAACd,OAAO;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBO,OAAO,EAAE7C,sBAAuB;QAChCuC,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,EACf;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAER/E,OAAA,CAAClC,IAAI;MAAC8H,SAAS;MAACC,OAAO,EAAE,CAAE;MAAArB,QAAA,EACxBhE,QAAQ,CAACsF,GAAG,CAAElD,QAAQ,iBACrB5C,OAAA,CAAClC,IAAI;QAACiI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eAC9BxE,OAAA,CAACjC,IAAI;UAAAyG,QAAA,gBACHxE,OAAA,CAAChC,WAAW;YAAAwG,QAAA,gBACVxE,OAAA,CAACtC,GAAG;cAAC6G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxE,OAAA,CAAClB,gBAAgB;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB/E,OAAA,CAACrC,UAAU;gBAAC8G,OAAO,EAAC,IAAI;gBAAC0B,SAAS,EAAC,KAAK;gBAAA3B,QAAA,EACrC5B,QAAQ,CAACtB;cAAI;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN/E,OAAA,CAACrC,UAAU;cAAC8G,OAAO,EAAC,OAAO;cAACiB,KAAK,EAAC,gBAAgB;cAACV,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBAC/DxE,OAAA;gBAAAwE,QAAA,EAAQ;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACnC,QAAQ,CAACrB,WAAW,IAAI,KAAK;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACb/E,OAAA,CAACtC,GAAG;cAACsH,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBACxDxE,OAAA,CAACrC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAACiB,KAAK,EAAC,gBAAgB;gBAACV,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,gBAC/DxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,aAC5B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/E,OAAA,CAACtB,UAAU;gBACT2H,IAAI,EAAC,OAAO;gBACZf,OAAO,EAAEA,CAAA,KAAM;kBACb3D,eAAe,CAAC;oBACdC,IAAI,EAAE,IAAI;oBACVC,OAAO,EAAE,+FAA+F;oBACxGC,QAAQ,EAAE;kBACZ,CAAC,CAAC;gBACJ,CAAE;gBACFwE,KAAK,EAAC,6BAA6B;gBAAA9B,QAAA,eAEnCxE,OAAA,CAACV,QAAQ;kBAACiH,QAAQ,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN/E,OAAA,CAACtC,GAAG;cAACsH,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBACxDxE,OAAA,CAACrC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAACiB,KAAK,EAAC,gBAAgB;gBAACV,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,gBAC/DxE,OAAA;kBAAAwE,QAAA,EAAQ;gBAAe;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnC,QAAQ,CAACU,cAAc,IAAI,KAAK;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACb/E,OAAA,CAACtB,UAAU;gBACT2H,IAAI,EAAC,OAAO;gBACZf,OAAO,EAAEA,CAAA,KAAM;kBACblB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1B,QAAQ,CAACU,cAAc,CAAC;kBACtD3B,eAAe,CAAC;oBACdC,IAAI,EAAE,IAAI;oBACVC,OAAO,EAAE,sCAAsC;oBAC/CC,QAAQ,EAAE;kBACZ,CAAC,CAAC;gBACJ,CAAE;gBACFwE,KAAK,EAAC,sBAAsB;gBAAA9B,QAAA,eAE5BxE,OAAA,CAACZ,eAAe;kBAACmH,QAAQ,EAAC;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACd/E,OAAA,CAAC/B,WAAW;YAAAuG,QAAA,gBACVxE,OAAA,CAACnC,MAAM;cACL4G,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,gBAAgB;cAC1Be,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAAChB,QAAQ,CAAE;cAAA4B,QAAA,EAC/C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/E,OAAA,CAACnC,MAAM;cACL4G,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,cAAc;cACxBc,SAAS,eAAErF,OAAA,CAAChB,UAAU;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BO,OAAO,EAAEA,CAAA,KAAM3C,sBAAsB,CAACC,QAAQ,CAAE;cAAA4B,QAAA,EACjD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAnE6BnC,QAAQ,CAACa,WAAW;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoEpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGD/E,OAAA,CAAC9B,MAAM;MAAC0D,IAAI,EAAEd,gBAAiB;MAAC0F,OAAO,EAAE9D,uBAAwB;MAAA8B,QAAA,gBAC/DxE,OAAA,CAAC1B,WAAW;QAAAkG,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9C/E,OAAA,CAAC5B,aAAa;QAAAoG,QAAA,gBACZxE,OAAA,CAAC3B,iBAAiB;UAAAmG,QAAA,EAAC;QAEnB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpB/E,OAAA,CAACzB,SAAS;UACRkI,SAAS;UACTC,MAAM,EAAC,OAAO;UACd1D,IAAI,EAAC,MAAM;UACX2D,KAAK,EAAC,eAAe;UACrBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTpC,OAAO,EAAC,UAAU;UAClBxB,KAAK,EAAE7B,eAAe,CAACE,IAAK;UAC5BwF,QAAQ,EAAEhE,iBAAkB;UAC5BiE,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/E,OAAA,CAACzB,SAAS;UACRmI,MAAM,EAAC,OAAO;UACd1D,IAAI,EAAC,aAAa;UAClB2D,KAAK,EAAC,aAAa;UACnBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTpC,OAAO,EAAC,UAAU;UAClBxB,KAAK,EAAE7B,eAAe,CAACG,WAAY;UACnCuF,QAAQ,EAAEhE;QAAkB;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF/E,OAAA,CAACzB,SAAS;UACRmI,MAAM,EAAC,OAAO;UACd1D,IAAI,EAAC,mBAAmB;UACxB2D,KAAK,EAAC,UAAU;UAChBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTpC,OAAO,EAAC,UAAU;UAClBxB,KAAK,EAAE7B,eAAe,CAACI,iBAAkB;UACzCsF,QAAQ,EAAEhE,iBAAkB;UAC5BiE,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/E,OAAA,CAACzB,SAAS;UACRmI,MAAM,EAAC,OAAO;UACd1D,IAAI,EAAC,mBAAmB;UACxB2D,KAAK,EAAC,mBAAmB;UACzBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTpC,OAAO,EAAC,UAAU;UAClBxB,KAAK,EAAE7B,eAAe,CAACK,iBAAkB;UACzCqF,QAAQ,EAAEhE,iBAAkB;UAC5BiE,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEW,CAAC,eAChB/E,OAAA,CAAC7B,aAAa;QAAAqG,QAAA,gBACZxE,OAAA,CAACnC,MAAM;UAAC4G,OAAO,EAAC,WAAW;UAACa,OAAO,EAAE5C,uBAAwB;UAAA8B,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9E/E,OAAA,CAACnC,MAAM;UAACyH,OAAO,EAAEnC,oBAAqB;UAACsB,OAAO,EAAC,WAAW;UAACF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAEtF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/E,OAAA,CAAC9B,MAAM;MAAC0D,IAAI,EAAEZ,gBAAiB;MAACwF,OAAO,EAAE3D,uBAAwB;MAAA2B,QAAA,gBAC/DxE,OAAA,CAAC1B,WAAW;QAAAkG,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3C/E,OAAA,CAAC5B,aAAa;QAAAoG,QAAA,eACZxE,OAAA,CAAC3B,iBAAiB;UAAAmG,QAAA,GAAC,0DACsC,EAACtD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEI,IAAI,EAAC,wEAEjF;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB/E,OAAA,CAAC7B,aAAa;QAAAqG,QAAA,gBACZxE,OAAA,CAACnC,MAAM;UAAC4G,OAAO,EAAC,WAAW;UAACa,OAAO,EAAEzC,uBAAwB;UAAA2B,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9E/E,OAAA,CAACnC,MAAM;UAACyH,OAAO,EAAE/B,oBAAqB;UAACkB,OAAO,EAAC,WAAW;UAACF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAEpF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/E,OAAA,CAACxB,QAAQ;MACPoD,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxBoF,gBAAgB,EAAE,IAAK;MACvBR,OAAO,EAAE1C,uBAAwB;MACjCmD,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA3C,QAAA,eAE3DxE,OAAA,CAACvB,KAAK;QAAC+H,OAAO,EAAE1C,uBAAwB;QAAChC,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAACkD,EAAE,EAAE;UAAEoC,KAAK,EAAE;QAAO,CAAE;QAAA5C,QAAA,eAC9FxE,OAAA;UAAKqH,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAW,CAAE;UAAA9C,QAAA,EAAE9C,YAAY,CAACG;QAAO;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC7E,EAAA,CA5ZID,QAAQ;EAAA,QACwDL,OAAO,EAC1DC,WAAW;AAAA;AAAA0H,EAAA,GAFxBtH,QAAQ;AA8Zd,eAAeA,QAAQ;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}