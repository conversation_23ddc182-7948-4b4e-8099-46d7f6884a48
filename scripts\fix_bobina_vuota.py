#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di correzione per ripristinare il valore corretto di id_bobina per i cavi non posati.
Imposta id_bobina = NULL per tutti i cavi con stato_installazione = 'Da installare' che hanno id_bobina = 'BOBINA_VUOTA'.
"""

import sys
import os
import logging
from datetime import datetime

# Aggiungi la directory principale al path per importare i moduli
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importa il modulo database_pg
from modules.database_pg import Database

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fix_bobina_vuota():
    """
    Corregge i cavi non posati che hanno erroneamente id_bobina = 'BOBINA_VUOTA'.
    Imposta id_bobina = NULL per tutti i cavi con stato_installazione = 'Da installare'.
    """
    db = Database()

    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()

            # Aggiorna i cavi con stato_installazione = 'Da installare' e id_bobina = 'BOBINA_VUOTA' a id_bobina = NULL
            cursor.execute("""
                UPDATE cavi
                SET id_bobina = NULL
                WHERE stato_installazione = 'Da installare' AND id_bobina = 'BOBINA_VUOTA'
            """)

            rows_updated = cursor.rowcount
            logging.info(f"✅ {rows_updated} cavi aggiornati da id_bobina='BOBINA_VUOTA' a id_bobina=NULL")

            conn.commit()
            logging.info("✅ Correzione completata con successo")
            return True

    except Exception as e:
        logging.error(f"❌ Errore durante la correzione: {str(e)}")
        return False

if __name__ == "__main__":
    fix_bobina_vuota()
