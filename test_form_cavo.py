#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per verificare che il form di inserimento nuovo cavo gestisca correttamente i valori null.
"""

import requests
import json
import sys
import time

# Configurazione
BASE_URL = "http://localhost:8001"
CANTIERE_ID = 1

def test_login():
    """Test di login per ottenere il token di autenticazione."""
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", data=login_data)
        if response.status_code == 200:
            token = response.json().get("access_token")
            print(f"✅ Login riuscito, token ottenuto")
            return token
        else:
            print(f"❌ Login fallito: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Errore durante il login: {str(e)}")
        return None

def test_inserimento_nuovo_cavo(token):
    """Test per inserire un nuovo cavo con alcuni campi null/vuoti."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Genera un ID univoco per il test
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    test_id = f"TEST_NULL_{timestamp}"

    # Dati per il nuovo cavo con alcuni campi null/vuoti per testare la gestione
    cavo_data = {
        "id_cavo": test_id,
        "revisione_ufficiale": "Rev 1.0",
        "sistema": "Sistema Test",
        "utility": "Test Utility",
        "colore_cavo": "Rosso",
        "tipologia": "MT",
        "n_conduttori": "3",
        "sezione": "16",
        "sh": "SI",
        "ubicazione_partenza": "Partenza Test",
        "utenza_partenza": None,  # Campo null
        "descrizione_utenza_partenza": "",  # Campo vuoto
        "ubicazione_arrivo": "Arrivo Test",
        "utenza_arrivo": None,  # Campo null
        "descrizione_utenza_arrivo": "",  # Campo vuoto
        "metri_teorici": 100.0,
        "metratura_reale": 0.0,
        "responsabile_posa": None,  # Campo null
        "id_bobina": None,  # Campo null
        "stato_installazione": "Non installato",
        "collegamenti": None,  # Campo null
        "responsabile_partenza": "",  # Campo vuoto
        "responsabile_arrivo": "",  # Campo vuoto
        "comanda_posa": None,  # Campo null
        "comanda_partenza": None,  # Campo null
        "comanda_arrivo": None  # Campo null
    }
    
    try:
        print(f"🔄 Test inserimento nuovo cavo con campi null/vuoti...")
        print(f"   Cantiere ID: {CANTIERE_ID}")
        print(f"   Cavo ID: {cavo_data['id_cavo']}")
        print(f"   Campi null: utenza_partenza, utenza_arrivo, responsabile_posa, id_bobina, collegamenti, comanda_*")
        print(f"   Campi vuoti: descrizione_utenza_*, responsabile_*")

        response = requests.post(
            f"{BASE_URL}/api/cavi/{CANTIERE_ID}",
            headers=headers,
            json=cavo_data
        )

        if response.status_code in [200, 201]:
            result = response.json()
            print(f"✅ Nuovo cavo inserito con successo!")
            print(f"   ID Cavo: {result.get('id_cavo')}")
            print(f"   Stato installazione: {result.get('stato_installazione')}")
            print(f"   Utenza partenza: {result.get('utenza_partenza')} (era null)")
            print(f"   Responsabile posa: {result.get('responsabile_posa')} (era null)")
            return test_id
        else:
            print(f"❌ Errore durante l'inserimento: {response.status_code}")
            print(f"   Dettaglio: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        return False

def test_verifica_cavo_inserito(token, cavo_id):
    """Verifica che il cavo sia stato inserito correttamente."""
    headers = {
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.get(
            f"{BASE_URL}/api/cavi/{CANTIERE_ID}/{cavo_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            cavo = response.json()
            print(f"✅ Cavo verificato nel database:")
            print(f"   ID Cavo: {cavo.get('id_cavo')}")
            print(f"   Utenza partenza: {repr(cavo.get('utenza_partenza'))}")
            print(f"   Utenza arrivo: {repr(cavo.get('utenza_arrivo'))}")
            print(f"   Responsabile posa: {repr(cavo.get('responsabile_posa'))}")
            print(f"   ID Bobina: {repr(cavo.get('id_bobina'))}")
            print(f"   Collegamenti: {repr(cavo.get('collegamenti'))}")
            
            # Verifica che i campi null siano gestiti correttamente
            null_fields = ['utenza_partenza', 'utenza_arrivo', 'responsabile_posa', 'id_bobina', 'collegamenti']
            for field in null_fields:
                value = cavo.get(field)
                if value is None or value == "":
                    print(f"   ✅ Campo {field}: correttamente null/vuoto")
                else:
                    print(f"   ⚠️ Campo {field}: valore inaspettato '{value}'")
            
            return True
        else:
            print(f"❌ Errore durante la verifica: {response.status_code}")
            print(f"   Dettaglio: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante la verifica: {str(e)}")
        return False

def cleanup_test_cavo(token, cavo_id):
    """Rimuove il cavo di test."""
    headers = {
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.delete(
            f"{BASE_URL}/api/cavi/{CANTIERE_ID}/{cavo_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            print(f"✅ Cavo di test rimosso con successo")
        else:
            print(f"⚠️ Impossibile rimuovere il cavo di test: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️ Errore durante la rimozione del cavo di test: {str(e)}")

def main():
    """Funzione principale del test."""
    print("🧪 Test Form Cavo - Gestione valori null/vuoti")
    print("=" * 50)
    
    # Attendi che il server sia pronto
    print("⏳ Attesa avvio del server...")
    time.sleep(3)
    
    # Test di login
    token = test_login()
    if not token:
        print("❌ Test fallito: impossibile ottenere il token di autenticazione")
        sys.exit(1)
    
    # Test inserimento nuovo cavo con campi null
    cavo_id = test_inserimento_nuovo_cavo(token)
    if not cavo_id:
        print("❌ Test fallito: errore durante l'inserimento del nuovo cavo")
        sys.exit(1)

    # Verifica cavo inserito
    success = test_verifica_cavo_inserito(token, cavo_id)
    if not success:
        print("❌ Test fallito: errore durante la verifica del cavo")
        cleanup_test_cavo(token, cavo_id)
        sys.exit(1)

    # Cleanup
    cleanup_test_cavo(token, cavo_id)
    
    print("\n✅ Tutti i test sono stati completati con successo!")
    print("🎉 Il form gestisce correttamente i valori null/vuoti")

if __name__ == "__main__":
    main()
