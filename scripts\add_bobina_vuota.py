#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per aggiungere il record BOBINA_VUOTA alla tabella parco_cavi.
Questo script è necessario per supportare la funzionalità "BOBINA_VUOTA"
mantenendo la compatibilità con la logica della CLI originale.
"""

import sys
import os
import logging
from datetime import datetime

# Aggiungi la directory principale al path per importare i moduli
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importa il modulo database_pg
from modules.database_pg import Database

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def add_bobina_vuota():
    """
    Aggiunge il record BOBINA_VUOTA alla tabella parco_cavi se non esiste già.
    """
    db = Database()

    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()

            # Verifica se il record BOBINA_VUOTA esiste già
            cursor.execute("SELECT id_bobina FROM parco_cavi WHERE id_bobina = 'BOBINA_VUOTA'")
            if cursor.fetchone():
                logging.info("✅ Il record BOBINA_VUOTA esiste già nella tabella parco_cavi")
                return True

            # Inserisci il record BOBINA_VUOTA
            cursor.execute("""
                INSERT INTO parco_cavi (
                    id_bobina, numero_bobina, utility, tipologia,
                    n_conduttori, sezione, metri_totali, metri_residui,
                    stato_bobina, ubicazione_bobina, fornitore, n_ddt, data_ddt, configurazione, id_cantiere
                ) VALUES (
                    'BOBINA_VUOTA', 'VUOTA', 'N/A', 'N/A',
                    '0', '0', 0, 0,
                    'Terminata', 'N/A', 'N/A', 'N/A', NULL, 'n', NULL
                )
            """)

            conn.commit()
            logging.info("✅ Record BOBINA_VUOTA aggiunto con successo alla tabella parco_cavi")
            return True

    except Exception as e:
        logging.error(f"❌ Errore durante l'aggiunta del record BOBINA_VUOTA: {str(e)}")
        return False

if __name__ == "__main__":
    add_bobina_vuota()
