# -*- coding: utf-8 -*-
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Optional
from modules.database import database_connection

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def aggiorna_collegamento(id_cantiere: int, id_cavo: str, lato: str, responsabile: str) -> bool:
    """
    Aggiorna lo stato di collegamento di un cavo.

    Args:
        id_cantiere: ID del cantiere
        id_cavo: ID del cavo
        lato: 'partenza' o 'arrivo'
        responsabile: Responsabile del collegamento

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            # Verifica che il cavo esista ed è posato
            c.execute("""
                SELECT id_cavo, collegamenti, responsabile_partenza, responsabile_arrivo
                FROM Cavi
                WHERE id_cavo = ? AND id_cantiere = ? AND stato_installazione = 'Installato'
            """, (id_cavo, id_cantiere))

            cavo = c.fetchone()
            if not cavo:
                print(f"❌ Il cavo {id_cavo} non esiste o non è posato nel cantiere!")
                return False

            # Ottieni lo stato attuale dei collegamenti
            collegamenti = cavo['collegamenti'] or 0

            # Aggiorna lo stato dei collegamenti in base al lato
            if lato == 'partenza':
                # Se il lato partenza è già collegato, non fare nulla
                if collegamenti & 1:
                    print(f"❌ Il lato partenza del cavo {id_cavo} è già collegato!")
                    return False

                # Aggiorna il flag dei collegamenti (aggiunge 1 per il lato partenza)
                nuovo_stato = collegamenti | 1

                # Aggiorna il database
                c.execute("""
                    UPDATE Cavi
                    SET collegamenti = ?, responsabile_partenza = ?
                    WHERE id_cavo = ? AND id_cantiere = ?
                """, (nuovo_stato, responsabile, id_cavo, id_cantiere))

            elif lato == 'arrivo':
                # Se il lato arrivo è già collegato, non fare nulla
                if collegamenti & 2:
                    print(f"❌ Il lato arrivo del cavo {id_cavo} è già collegato!")
                    return False

                # Aggiorna il flag dei collegamenti (aggiunge 2 per il lato arrivo)
                nuovo_stato = collegamenti | 2

                # Aggiorna il database
                c.execute("""
                    UPDATE Cavi
                    SET collegamenti = ?, responsabile_arrivo = ?
                    WHERE id_cavo = ? AND id_cantiere = ?
                """, (nuovo_stato, responsabile, id_cavo, id_cantiere))

            else:
                print("❌ Lato non valido! Deve essere 'partenza' o 'arrivo'.")
                return False

            conn.commit()
            print(f"✅ Collegamento {lato} del cavo {id_cavo} aggiornato con successo!")
            return True

    except Exception as e:
        logging.error(f"Errore durante l'aggiornamento del collegamento: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False

def visualizza_collegamenti(id_cantiere: int, filtro: str = None) -> bool:
    """
    Visualizza tutti i cavi con i loro stati di collegamento.

    Args:
        id_cantiere: ID del cantiere
        filtro: Filtro opzionale per ID cavo

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            # Query base
            query = """
                SELECT
                    id_cavo, collegamenti, responsabile_partenza, responsabile_arrivo,
                    ubicazione_partenza, ubicazione_arrivo, stato_installazione
                FROM Cavi
                WHERE id_cantiere = ? AND stato_installazione = 'Installato'
            """

            params = [id_cantiere]

            # Aggiungi filtro se specificato
            if filtro:
                query += " AND id_cavo LIKE ?"
                params.append(f"%{filtro}%")

            # Ordina per ID cavo
            query += " ORDER BY id_cavo"

            c.execute(query, params)
            cavi = c.fetchall()

            if not cavi:
                print("\nNessun cavo collegato trovato.")
                return True

            # Visualizza i cavi e i loro collegamenti
            print("\n=== STATO COLLEGAMENTI CAVI ===")
            print("{:<15} {:<15} {:<15} {:<20} {:<20} {:<15}".format(
                "ID Cavo", "Stato", "Lato Partenza", "Lato Arrivo", "Resp. Partenza", "Resp. Arrivo"
            ))
            print("-" * 100)

            for cavo in cavi:
                collegamenti = cavo['collegamenti'] or 0

                # Determina lo stato dei collegamenti
                if collegamenti == 0:
                    stato = "Non collegato"
                elif collegamenti == 1:
                    stato = "Solo partenza"
                elif collegamenti == 2:
                    stato = "Solo arrivo"
                elif collegamenti == 3:
                    stato = "Completo"
                else:
                    stato = f"Sconosciuto ({collegamenti})"

                # Responsabili
                resp_partenza = cavo['responsabile_partenza'] or "-"
                resp_arrivo = cavo['responsabile_arrivo'] or "-"

                print("{:<15} {:<15} {:<15} {:<20} {:<20} {:<15}".format(
                    cavo['id_cavo'],
                    stato,
                    "Collegato" if collegamenti & 1 else "Non collegato",
                    "Collegato" if collegamenti & 2 else "Non collegato",
                    resp_partenza[:20],
                    resp_arrivo[:15]
                ))

            return True

    except Exception as e:
        logging.error(f"Errore durante la visualizzazione dei collegamenti: {str(e)}")
        print(f"❌ Errore: {str(e)}")
        return False

def gestisci_collegamento_intelligente(id_cantiere: int) -> bool:
    """
    Gestisce in modo intelligente il collegamento di un cavo, verificando lo stato attuale
    e chiedendo all'utente cosa vuole collegare.

    Args:
        id_cantiere: ID del cantiere

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Richiedi l'ID del cavo
        print("\n🔗 GESTIONE COLLEGAMENTO CAVO")
        id_cavo = input("\nInserisci l'ID del cavo da collegare: ").strip().upper()
        if not id_cavo:
            print("❌ L'ID cavo è obbligatorio!")
            return False

        # Verifica che il cavo esista ed è posato
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT id_cavo, collegamenti, responsabile_partenza, responsabile_arrivo,
                       ubicazione_partenza, ubicazione_arrivo, stato_installazione
                FROM Cavi
                WHERE id_cavo = ? AND id_cantiere = ?
            """, (id_cavo, id_cantiere))

            cavo = c.fetchone()
            if not cavo:
                print(f"❌ Il cavo {id_cavo} non esiste nel cantiere!")
                return False

            if cavo['stato_installazione'] != 'Installato':
                print(f"❌ Il cavo {id_cavo} non è ancora posato! Solo i cavi posati possono essere collegati.")
                return False

            # Ottieni lo stato attuale dei collegamenti
            collegamenti = cavo['collegamenti'] or 0
            lato_partenza_collegato = (collegamenti & 1) == 1
            lato_arrivo_collegato = (collegamenti & 2) == 2

            # Mostra le informazioni del cavo
            print("\n[INFORMAZIONI CAVO]")
            print(f"ID Cavo: {cavo['id_cavo']}")
            print(f"Partenza (FROM): {cavo['ubicazione_partenza']}")
            print(f"Arrivo (TO): {cavo['ubicazione_arrivo']}")
            print("\n[STATO COLLEGAMENTI]")
            print(f"Lato Partenza: {'Collegato' if lato_partenza_collegato else 'Non collegato'}")
            if lato_partenza_collegato:
                print(f"  Responsabile: {cavo['responsabile_partenza']}")
            print(f"Lato Arrivo: {'Collegato' if lato_arrivo_collegato else 'Non collegato'}")
            if lato_arrivo_collegato:
                print(f"  Responsabile: {cavo['responsabile_arrivo']}")

            # Chiedi all'utente cosa vuole fare
            modifiche = False

            # Gestione lato partenza
            if not lato_partenza_collegato:
                scelta_partenza = input("\nCollegare il lato partenza? (s/n): ").strip().lower()
                if scelta_partenza == 's':
                    responsabile = input("Responsabile del collegamento (lascia vuoto per 'cantiere'): ").strip()
                    if not responsabile:
                        responsabile = "cantiere"

                    # Conferma
                    conferma = input(f"Confermi il collegamento del lato partenza con responsabile '{responsabile}'? (s/n): ").strip().lower()
                    if conferma == 's':
                        aggiorna_collegamento(id_cantiere, id_cavo, 'partenza', responsabile)
                        modifiche = True
                    else:
                        print("Collegamento lato partenza annullato.")
            else:
                scelta_partenza = input("\nScollegare il lato partenza? (s/n): ").strip().lower()
                if scelta_partenza == 's':
                    # Conferma
                    conferma = input("Confermi lo scollegamento del lato partenza? (s/n): ").strip().lower()
                    if conferma == 's':
                        # Rimuovi il flag di collegamento lato partenza
                        c.execute("""
                            UPDATE Cavi
                            SET collegamenti = collegamenti & ~1, responsabile_partenza = NULL
                            WHERE id_cavo = ? AND id_cantiere = ?
                        """, (id_cavo, id_cantiere))
                        conn.commit()
                        print(f"✅ Lato partenza del cavo {id_cavo} scollegato con successo!")
                        modifiche = True
                    else:
                        print("Scollegamento lato partenza annullato.")

            # Gestione lato arrivo
            if not lato_arrivo_collegato:
                scelta_arrivo = input("\nCollegare il lato arrivo? (s/n): ").strip().lower()
                if scelta_arrivo == 's':
                    responsabile = input("Responsabile del collegamento (lascia vuoto per 'cantiere'): ").strip()
                    if not responsabile:
                        responsabile = "cantiere"

                    # Conferma
                    conferma = input(f"Confermi il collegamento del lato arrivo con responsabile '{responsabile}'? (s/n): ").strip().lower()
                    if conferma == 's':
                        aggiorna_collegamento(id_cantiere, id_cavo, 'arrivo', responsabile)
                        modifiche = True
                    else:
                        print("Collegamento lato arrivo annullato.")
            else:
                scelta_arrivo = input("\nScollegare il lato arrivo? (s/n): ").strip().lower()
                if scelta_arrivo == 's':
                    # Conferma
                    conferma = input("Confermi lo scollegamento del lato arrivo? (s/n): ").strip().lower()
                    if conferma == 's':
                        # Rimuovi il flag di collegamento lato arrivo
                        c.execute("""
                            UPDATE Cavi
                            SET collegamenti = collegamenti & ~2, responsabile_arrivo = NULL
                            WHERE id_cavo = ? AND id_cantiere = ?
                        """, (id_cavo, id_cantiere))
                        conn.commit()
                        print(f"✅ Lato arrivo del cavo {id_cavo} scollegato con successo!")
                        modifiche = True
                    else:
                        print("Scollegamento lato arrivo annullato.")

            # Se sono state fatte modifiche, mostra lo stato finale
            if modifiche:
                print("\n[STATO FINALE DEI COLLEGAMENTI]")
                c.execute("""
                    SELECT collegamenti, responsabile_partenza, responsabile_arrivo
                    FROM Cavi
                    WHERE id_cavo = ? AND id_cantiere = ?
                """, (id_cavo, id_cantiere))

                cavo_aggiornato = c.fetchone()
                collegamenti = cavo_aggiornato['collegamenti'] or 0
                lato_partenza_collegato = (collegamenti & 1) == 1
                lato_arrivo_collegato = (collegamenti & 2) == 2

                print(f"Lato Partenza: {'Collegato' if lato_partenza_collegato else 'Non collegato'}")
                if lato_partenza_collegato:
                    print(f"  Responsabile: {cavo_aggiornato['responsabile_partenza']}")
                print(f"Lato Arrivo: {'Collegato' if lato_arrivo_collegato else 'Non collegato'}")
                if lato_arrivo_collegato:
                    print(f"  Responsabile: {cavo_aggiornato['responsabile_arrivo']}")
            else:
                print("\nNessuna modifica effettuata ai collegamenti.")

            return True

    except Exception as e:
        logging.error(f"Errore durante la gestione intelligente dei collegamenti: {str(e)}")
        print(f"❌ Errore: {str(e)}")
        return False


def gestisci_collegamenti(id_cantiere: int) -> None:
    """
    Menu principale per la gestione dei collegamenti.

    Args:
        id_cantiere: ID del cantiere
    """
    while True:
        print("\n" + "=" * 60)
        print("🔗 GESTIONE COLLEGAMENTI")
        print("=" * 60)
        print("\nOpzioni disponibili:")
        print(" 1. Visualizza collegamenti")
        print(" 2. Gestisci collegamento cavo")
        print(" 3. Cerca collegamenti per cavo")
        print(" 4. Torna al menu precedente")

        scelta = input("\nSeleziona un'opzione (1-4): ").strip()

        if scelta == "1":
            visualizza_collegamenti(id_cantiere)
        elif scelta == "2":
            gestisci_collegamento_intelligente(id_cantiere)
        elif scelta == "3":
            filtro = input("Inserisci l'ID del cavo da cercare: ").strip().upper()
            visualizza_collegamenti(id_cantiere, filtro)
        elif scelta == "4":
            break
        else:
            print("❌ Opzione non valida!")
