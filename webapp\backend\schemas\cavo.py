from pydantic import BaseModel
from typing import Optional, Literal
from datetime import datetime

class CavoBase(BaseModel):
    """Schema base per i cavi."""
    id_cavo: str
    revisione_ufficiale: str
    sistema: Optional[str] = None
    utility: Optional[str] = None
    colore_cavo: Optional[str] = None
    tipologia: Optional[str] = None
    n_conduttori: Optional[str] = None
    sezione: Optional[str] = None
    # Rinominato da SH a sh per corrispondere alla struttura del database
    sh: Optional[str] = None
    ubicazione_partenza: Optional[str] = None
    utenza_partenza: Optional[str] = None
    descrizione_utenza_partenza: Optional[str] = None
    ubicazione_arrivo: Optional[str] = None
    utenza_arrivo: Optional[str] = None
    descrizione_utenza_arrivo: Optional[str] = None
    metri_teorici: Optional[float] = None
    metratura_reale: Optional[float] = None
    responsabile_posa: Optional[str] = None
    id_bobina: Optional[str] = None
    stato_installazione: Optional[str] = None
    responsabile_partenza: Optional[str] = None
    responsabile_arrivo: Optional[str] = None
    comanda_posa: Optional[str] = None
    comanda_partenza: Optional[str] = None
    comanda_arrivo: Optional[str] = None

class CavoCreate(CavoBase):
    """Schema per la creazione di un cavo."""
    pass

class CavoUpdate(BaseModel):
    """Schema per l'aggiornamento di un cavo."""
    revisione_ufficiale: Optional[str] = None
    sistema: Optional[str] = None
    utility: Optional[str] = None
    colore_cavo: Optional[str] = None
    tipologia: Optional[str] = None
    n_conduttori: Optional[str] = None
    sezione: Optional[str] = None
    # Rinominato da SH a sh per corrispondere alla struttura del database
    sh: Optional[str] = None
    ubicazione_partenza: Optional[str] = None
    utenza_partenza: Optional[str] = None
    descrizione_utenza_partenza: Optional[str] = None
    ubicazione_arrivo: Optional[str] = None
    utenza_arrivo: Optional[str] = None
    descrizione_utenza_arrivo: Optional[str] = None
    metri_teorici: Optional[float] = None
    metratura_reale: Optional[float] = None
    responsabile_posa: Optional[str] = None
    id_bobina: Optional[str] = None
    stato_installazione: Optional[str] = None
    responsabile_partenza: Optional[str] = None
    responsabile_arrivo: Optional[str] = None
    comanda_posa: Optional[str] = None
    comanda_partenza: Optional[str] = None
    comanda_arrivo: Optional[str] = None

class CavoInDB(CavoBase):
    """Schema per un cavo nel database."""
    id_cantiere: int
    modificato_manualmente: int
    timestamp: datetime
    collegamenti: Optional[int] = 0

    class Config:
        orm_mode = True

class MetriPosatiUpdate(BaseModel):
    """Schema per l'aggiornamento dei metri posati di un cavo."""
    metri_posati: float
    id_bobina: Optional[str] = None
    data_posa: Optional[datetime] = None
    force_over: Optional[bool] = False  # Se True, forza l'operazione anche se la bobina ha metri residui insufficienti

class BobinaUpdate(BaseModel):
    """Schema per l'aggiornamento della bobina di un cavo."""
    id_bobina: Optional[str] = None

class CollegamentoUpdate(BaseModel):
    """Schema per l'aggiornamento del collegamento di un cavo."""
    lato: str  # 'partenza' o 'arrivo'
    responsabile: Optional[str] = 'cantiere'


class MarkAsSpareUpdate(BaseModel):
    """Schema per marcare un cavo come SPARE."""
    force: Optional[bool] = False  # Se True, forza la marcatura anche se il cavo è installato


class DeleteCavoOptions(BaseModel):
    """Schema per le opzioni di eliminazione di un cavo."""
    mode: Literal['spare', 'delete']  # 'spare' per marcare come SPARE, 'delete' per eliminare definitivamente
