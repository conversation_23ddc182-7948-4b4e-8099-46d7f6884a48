#!/bin/bash

echo "Verifica dei cavi SPARE nel database..."
echo

# Imposta il percorso Python
PYTHON_PATH=python3

# Verifica se è stato specificato un ID cantiere
if [ -z "$1" ]; then
    echo "Esecuzione senza parametri - verranno mostrati tutti i cantieri"
    $PYTHON_PATH webapp/backend/scripts/check_spare_cables.py
else
    echo "Verifica per il cantiere con ID $1"
    if [ -z "$2" ]; then
        $PYTHON_PATH webapp/backend/scripts/check_spare_cables.py $1
    else
        echo "Verifica per il cavo con ID $2"
        $PYTHON_PATH webapp/backend/scripts/check_spare_cables.py $1 $2
    fi
fi

echo
echo "Premi INVIO per continuare..."
read
