#!/usr/bin/env python3
"""
Script di test per verificare il calcolo della media giornaliera realistica.
Simula diversi scenari per testare la logica implementata.
"""

from datetime import datetime, timedelta
from typing import List, Tuple, Optional

def _calcola_data_completamento_lavorativa(data_inizio: datetime.date, giorni_lavorativi_necessari: int, posa_storica: List[Tuple]) -> Optional[datetime.date]:
    """
    Calcola la data di completamento considerando solo i giorni lavorativi effettivi.
    Analizza lo storico per capire quali giorni della settimana si lavora normalmente.
    """
    if giorni_lavorativi_necessari <= 0:
        return data_inizio
    
    # Analizza lo storico per capire i pattern lavorativi
    giorni_settimana_lavorativi = set()
    
    if posa_storica:
        for row in posa_storica:
            data_posa = row[0]
            if data_posa:
                # 0=Lunedì, 6=Domenica
                giorno_settimana = data_posa.weekday()
                giorni_settimana_lavorativi.add(giorno_settimana)
    
    # Se non abbiamo dati storici, assume lun-ven (0-4)
    if not giorni_settimana_lavorativi:
        giorni_settimana_lavorativi = {0, 1, 2, 3, 4}  # Lun-Ven
    
    # Calcola la data di completamento
    data_corrente = data_inizio + timedelta(days=1)  # Inizia dal giorno successivo
    giorni_contati = 0
    
    # Limite di sicurezza per evitare loop infiniti
    max_iterazioni = giorni_lavorativi_necessari * 3
    iterazioni = 0
    
    while giorni_contati < giorni_lavorativi_necessari and iterazioni < max_iterazioni:
        if data_corrente.weekday() in giorni_settimana_lavorativi:
            giorni_contati += 1
        
        if giorni_contati < giorni_lavorativi_necessari:
            data_corrente += timedelta(days=1)
        
        iterazioni += 1
    
    return data_corrente if iterazioni < max_iterazioni else None

def test_scenario_1():
    """Test scenario 1: Lavoro solo nei giorni feriali (lun-ven)"""
    print("🧪 Test Scenario 1: Lavoro solo nei giorni feriali")
    
    # Simula dati di posa per 5 giorni feriali
    oggi = datetime.now().date()
    posa_storica = []
    
    for i in range(5):
        data_posa = oggi - timedelta(days=i*2)  # Solo giorni feriali
        if data_posa.weekday() < 5:  # Lun-Ven
            posa_storica.append((data_posa, 80.0))  # 80 metri al giorno
    
    # Calcoli
    metri_totali = 1000
    metri_posati = sum(row[1] for row in posa_storica)
    metri_residui = metri_totali - metri_posati
    giorni_lavorativi_effettivi = len(posa_storica)
    media_giornaliera = metri_posati / giorni_lavorativi_effettivi if giorni_lavorativi_effettivi > 0 else 0
    giorni_necessari = int(metri_residui / media_giornaliera) + (1 if metri_residui % media_giornaliera > 0 else 0)
    
    data_completamento = _calcola_data_completamento_lavorativa(oggi, giorni_necessari, posa_storica)
    
    print(f"  📊 Metri totali: {metri_totali}m")
    print(f"  ✅ Metri posati: {metri_posati}m")
    print(f"  ⏳ Metri residui: {metri_residui}m")
    print(f"  📅 Giorni lavorativi effettivi: {giorni_lavorativi_effettivi}")
    print(f"  📈 Media giornaliera: {media_giornaliera:.2f}m/giorno")
    print(f"  🎯 Giorni necessari: {giorni_necessari}")
    print(f"  📅 Data completamento stimata: {data_completamento}")
    print()

def test_scenario_2():
    """Test scenario 2: Lavoro anche nei weekend"""
    print("🧪 Test Scenario 2: Lavoro anche nei weekend")
    
    # Simula dati di posa per 7 giorni consecutivi
    oggi = datetime.now().date()
    posa_storica = []
    
    for i in range(7):
        data_posa = oggi - timedelta(days=i)
        posa_storica.append((data_posa, 100.0))  # 100 metri al giorno
    
    # Calcoli
    metri_totali = 1500
    metri_posati = sum(row[1] for row in posa_storica)
    metri_residui = metri_totali - metri_posati
    giorni_lavorativi_effettivi = len(posa_storica)
    media_giornaliera = metri_posati / giorni_lavorativi_effettivi if giorni_lavorativi_effettivi > 0 else 0
    giorni_necessari = int(metri_residui / media_giornaliera) + (1 if metri_residui % media_giornaliera > 0 else 0)
    
    data_completamento = _calcola_data_completamento_lavorativa(oggi, giorni_necessari, posa_storica)
    
    print(f"  📊 Metri totali: {metri_totali}m")
    print(f"  ✅ Metri posati: {metri_posati}m")
    print(f"  ⏳ Metri residui: {metri_residui}m")
    print(f"  📅 Giorni lavorativi effettivi: {giorni_lavorativi_effettivi}")
    print(f"  📈 Media giornaliera: {media_giornaliera:.2f}m/giorno")
    print(f"  🎯 Giorni necessari: {giorni_necessari}")
    print(f"  📅 Data completamento stimata: {data_completamento}")
    print()

def test_scenario_3():
    """Test scenario 3: Lavoro irregolare (alcuni giorni saltati)"""
    print("🧪 Test Scenario 3: Lavoro irregolare")
    
    # Simula dati di posa irregolari
    oggi = datetime.now().date()
    posa_storica = [
        (oggi - timedelta(days=1), 120.0),   # Ieri
        (oggi - timedelta(days=3), 90.0),    # 3 giorni fa
        (oggi - timedelta(days=5), 110.0),   # 5 giorni fa
        (oggi - timedelta(days=8), 80.0),    # 8 giorni fa
    ]
    
    # Calcoli
    metri_totali = 800
    metri_posati = sum(row[1] for row in posa_storica)
    metri_residui = metri_totali - metri_posati
    giorni_lavorativi_effettivi = len(posa_storica)
    media_giornaliera = metri_posati / giorni_lavorativi_effettivi if giorni_lavorativi_effettivi > 0 else 0
    giorni_necessari = int(metri_residui / media_giornaliera) + (1 if metri_residui % media_giornaliera > 0 else 0)
    
    data_completamento = _calcola_data_completamento_lavorativa(oggi, giorni_necessari, posa_storica)
    
    print(f"  📊 Metri totali: {metri_totali}m")
    print(f"  ✅ Metri posati: {metri_posati}m")
    print(f"  ⏳ Metri residui: {metri_residui}m")
    print(f"  📅 Giorni lavorativi effettivi: {giorni_lavorativi_effettivi}")
    print(f"  📈 Media giornaliera: {media_giornaliera:.2f}m/giorno")
    print(f"  🎯 Giorni necessari: {giorni_necessari}")
    print(f"  📅 Data completamento stimata: {data_completamento}")
    print()

if __name__ == "__main__":
    print("🚀 Test del Calcolo Media Giornaliera Realistica")
    print("=" * 50)
    print()
    
    test_scenario_1()
    test_scenario_2()
    test_scenario_3()
    
    print("✅ Test completati!")
