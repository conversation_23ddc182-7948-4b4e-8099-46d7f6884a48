from pydantic import BaseModel, Field
from typing import Optional

class Token(BaseModel):
    """Schema per il token di accesso."""
    access_token: str
    token_type: str
    user_id: int
    username: str
    role: str
    is_impersonated: Optional[bool] = False
    impersonated_id: Optional[int] = None
    impersonated_username: Optional[str] = None
    impersonated_role: Optional[str] = None

class TokenData(BaseModel):
    """Schema per i dati contenuti nel token JWT."""
    username: Optional[str] = None
    user_id: Optional[int] = None
    role: Optional[str] = None
    is_impersonated: Optional[bool] = False
    impersonated_id: Optional[int] = None
    impersonated_username: Optional[str] = None
    impersonated_role: Optional[str] = None

class UserLogin(BaseModel):
    """Schema per il login utente standard o admin."""
    username: str
    password: str

class CantiereLogin(BaseModel):
    """Schema per il login utente cantiere."""
    codice_univoco: str
    password: str
