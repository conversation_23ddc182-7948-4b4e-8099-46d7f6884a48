import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Stack,
  MenuItem,
  Alert,
  CircularProgress,
  Typography,
  Divider,
  Paper,
  IconButton,
  Tooltip,
  Grid
} from '@mui/material';
import {
  Save as SaveIcon,
  Warning as WarningIcon,
  Cancel as CancelIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { validateCavoData, validateField } from '../../utils/validationUtils';
import { redirectToVisualizzaCavi } from '../../utils/navigationUtils';

const SECTION_CONFIG = [
  {
    title: 'Informazioni Generali',
    collapsible: false,
    fields: [
      { name: 'id_cavo', label: 'ID Cavo', required: true, inputProps: { style: { textTransform: 'uppercase' } } },
      { name: 'utility', label: 'Utility', required: true },
      { name: 'sistema', label: '<PERSON>ste<PERSON>' }
    ]
  },
  {
    title: 'Caratteristiche Tecniche',
    collapsible: false,
    fields: [
      { name: 'colore_cavo', label: 'Colore Cavo' },
      { name: 'tipologia', label: 'Tipologia' },
      // n_conduttori field is now a spare field (kept in DB but hidden in UI)
      { name: 'sezione', label: 'Formazione' },
      // sh field is now a spare field (kept in DB but hidden in UI)
    ]
  },
  {
    title: 'Partenza',
    collapsible: true,
    fields: [
      { name: 'ubicazione_partenza', label: 'Ubicazione Partenza', gridSize: { xs: 12, sm: 6, md: 4 } },
      { name: 'utenza_partenza', label: 'Utenza Partenza', gridSize: { xs: 12, sm: 6, md: 4 } },
      { name: 'descrizione_utenza_partenza', label: 'Descrizione Utenza Partenza', gridSize: { xs: 12, sm: 6, md: 4 } }
    ]
  },
  {
    title: 'Arrivo',
    collapsible: true,
    fields: [
      { name: 'ubicazione_arrivo', label: 'Ubicazione Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } },
      { name: 'utenza_arrivo', label: 'Utenza Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } },
      { name: 'descrizione_utenza_arrivo', label: 'Descrizione Utenza Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } }
    ]
  },
  {
    title: 'Metratura',
    collapsible: false,
    fields: [
      { name: 'metri_teorici', label: 'Metri Teorici', required: true }
    ]
  }
];

const defaultData = {
  id_cavo: '',
  utility: '',
  sistema: '',
  colore_cavo: '',
  tipologia: '',
  n_conduttori: '',
  sezione: '',
  sh: 'N',
  ubicazione_partenza: '',
  utenza_partenza: '',
  descrizione_utenza_partenza: '',
  ubicazione_arrivo: '',
  utenza_arrivo: '',
  descrizione_utenza_arrivo: '',
  metri_teorici: '',
  metratura_reale: '0'
};

const CavoForm = ({ mode = 'add', initialData = {}, onSubmit, onSuccess, onError, onCancel }) => {
  const navigate = useNavigate();

  // Funzione per pulire i dati iniziali da valori null/undefined
  const cleanInitialData = (data) => {
    const cleaned = {};
    Object.keys(defaultData).forEach(key => {
      // Se il valore è null o undefined, usa il valore di default
      cleaned[key] = (data[key] !== null && data[key] !== undefined) ? data[key] : defaultData[key];
    });
    return cleaned;
  };

  const [formData, setFormData] = useState(cleanInitialData(initialData));
  const [formErrors, setFormErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [loading, setLoading] = useState(false);
  const [expanded, setExpanded] = useState({});
  const [showWarnings, setShowWarnings] = useState(false);

  // Inizializza lo stato di espansione delle sezioni collassabili
  useEffect(() => {
    const state = {};
    SECTION_CONFIG.forEach(s => { if (s.collapsible) state[s.title] = true; });
    setExpanded(state);
  }, []);

  // Gestisce l'espansione/collasso delle sezioni
  const toggleExpand = (title) => {
    setExpanded(prev => ({ ...prev, [title]: !prev[title] }));
  };

  // Gestisce i cambiamenti nei campi del form
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validazione in tempo reale
    const extra = name === 'metratura_reale' ? { metriTeorici: parseFloat(formData.metri_teorici || 0) } : {};
    const result = validateField(name, value, extra);

    // Assicurati che gli errori siano sempre stringhe o null
    const errorMessage = result.valid ? null :
      (typeof result.message === 'object' ?
        (result.message?.message || JSON.stringify(result.message)) :
        result.message);

    setFormErrors(prev => ({ ...prev, [name]: errorMessage }));

    setWarnings(prev => {
      // Assicurati che i warning siano sempre stringhe o null
      const warningMessage = result.warning ?
        (typeof result.message === 'object' ?
          (result.message?.message || JSON.stringify(result.message)) :
          result.message) :
        null;

      const newWarnings = { ...prev, [name]: warningMessage };
      // Mostra il banner di avviso se ci sono warning
      setShowWarnings(Object.values(newWarnings).some(w => w));
      return newWarnings;
    });
  };

  // Gestisce l'invio del form
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    const validation = validateCavoData(formData);

    if (!validation.isValid) {
      // Assicurati che gli errori siano sempre stringhe o null
      const processedErrors = {};
      Object.entries(validation.errors).forEach(([key, value]) => {
        processedErrors[key] = typeof value === 'object' ?
          (value?.message || JSON.stringify(value)) : value;
      });

      // Assicurati che i warning siano sempre stringhe o null
      const processedWarnings = {};
      Object.entries(validation.warnings).forEach(([key, value]) => {
        processedWarnings[key] = typeof value === 'object' ?
          (value?.message || JSON.stringify(value)) : value;
      });

      setFormErrors(processedErrors);
      setWarnings(processedWarnings);
      setShowWarnings(Object.values(processedWarnings).some(w => w));
      setLoading(false);
      onError('Ci sono errori nel form.');
      return;
    }

    try {
      const finalData = { 
        ...validation.validatedData, 
        id_cavo: validation.validatedData.id_cavo.toUpperCase(),
        metratura_reale: '0' // Set default value
      };
      await onSubmit(finalData);
      setLoading(false);
      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);
      redirectToVisualizzaCavi(navigate);
    } catch (err) {
      setLoading(false);
      onError(err.message);
    }
  };

  // Gestisce l'annullamento del form
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      redirectToVisualizzaCavi(navigate);
    }
  };

  // Renderizza un singolo campo del form
  const renderField = (field) => {
    // Gestisce i valori null/undefined per evitare la visualizzazione di "null"
    const fieldValue = formData[field.name] ?? '';

    // Gestisce i messaggi di errore e warning in modo più pulito
    const errorMessage = formErrors[field.name];
    const warningMessage = warnings[field.name];

    const helperText = errorMessage || warningMessage || '';

    return (
      <TextField
        select={field.type === 'select'}
        fullWidth
        size="small"
        margin="none"
        label={field.label}
        name={field.name}
        value={fieldValue}
        onChange={handleChange}
        error={!!errorMessage}
        helperText={helperText}
        required={field.required}
        variant="outlined"
        InputLabelProps={{
          shrink: true,
          sx: {
            fontWeight: field.required ? 600 : 400,
            color: field.required ? 'primary.main' : 'text.secondary'
          }
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            '&:hover fieldset': {
              borderColor: 'primary.main',
            },
            '&.Mui-focused fieldset': {
              borderWidth: 2,
            },
          },
          '& .MuiFormHelperText-root': {
            fontSize: '0.7rem',
            marginTop: '2px',
            lineHeight: 1.2,
            color: errorMessage ? 'error.main' : warningMessage ? 'warning.main' : 'text.secondary'
          }
        }}
        {...(field.inputProps || {})}
      >
        {field.options?.map(opt => (
          <MenuItem key={opt} value={opt}>{opt}</MenuItem>
        ))}
      </TextField>
    );
  };

  return (
    <Paper
      elevation={2}
      sx={{
        border: '1px solid #e0e0e0',
        borderRadius: 2,
        overflow: 'hidden',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
        boxShadow: '0 3px 15px rgba(0,0,0,0.08)',
        maxHeight: 'calc(100vh - 120px)',
        overflowY: 'auto'
      }}
    >
      <Box component="form" onSubmit={handleSubmit} noValidate>
        {/* Avviso di validazione */}
        {showWarnings && (
          <Alert
            severity="warning"
            icon={<WarningIcon />}
            sx={{
              borderRadius: 0,
              py: 0.5,
              backgroundColor: '#fff3cd',
              borderBottom: '1px solid #ffeaa7',
              '& .MuiAlert-message': {
                fontWeight: 500,
                fontSize: '0.85rem'
              }
            }}
          >
            Alcuni campi potrebbero necessitare revisione
          </Alert>
        )}

        {/* Sezioni del form */}
        {SECTION_CONFIG.map((section, index) => {
          const isExpanded = section.collapsible ? expanded[section.title] : true;

          return (
            <Box key={section.title} sx={{ borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #e8eaed' : 'none' }}>
              {/* Intestazione della sezione */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  px: 2.5,
                  py: 1,
                  background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                  borderBottom: isExpanded ? '1px solid #e8eaed' : 'none',
                  cursor: section.collapsible ? 'pointer' : 'default',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': section.collapsible ? {
                    background: 'linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%)',
                  } : {}
                }}
                onClick={() => section.collapsible && toggleExpand(section.title)}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 700,
                    color: '#495057',
                    fontSize: '0.9rem',
                    letterSpacing: '0.3px',
                    textTransform: 'uppercase'
                  }}
                >
                  {section.title}
                </Typography>

                {section.collapsible && (
                  <IconButton
                    size="small"
                    sx={{ ml: 1 }}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleExpand(section.title);
                    }}
                  >
                    {isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                  </IconButton>
                )}
              </Box>

              {/* Contenuto della sezione */}
              {isExpanded && (
                <Box sx={{
                  p: 2,
                  backgroundColor: '#ffffff',
                  borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #f1f3f4' : 'none'
                }}>
                  <Grid container spacing={1.5}>
                    {section.fields.map(field => (
                      <Grid
                        item
                        xs={field.gridSize?.xs || 12}
                        sm={field.gridSize?.sm || 6}
                        md={field.gridSize?.md || 4}
                        key={field.name}
                      >
                        {renderField(field)}
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Box>
          );
        })}

        {/* Pulsanti di azione */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 1.5,
            p: 2,
            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
            borderTop: '1px solid #e8eaed'
          }}
        >
          <Tooltip title="Annulla e torna indietro" arrow>
            <Button
              variant="outlined"
              color="inherit"
              onClick={handleCancel}
              disabled={loading}
              startIcon={<CancelIcon />}
              size="medium"
              sx={{
                borderColor: '#6c757d',
                color: '#6c757d',
                fontWeight: 600,
                px: 2.5,
                py: 0.8,
                '&:hover': {
                  borderColor: '#495057',
                  backgroundColor: '#f8f9fa',
                  color: '#495057'
                }
              }}
            >
              Annulla
            </Button>
          </Tooltip>

          <Tooltip title={mode === 'add' ? 'Salva nuovo cavo' : 'Salva modifiche al cavo'} arrow>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={18} color="inherit" /> : <SaveIcon />}
              disabled={loading}
              size="medium"
              sx={{
                fontWeight: 600,
                px: 3,
                py: 0.8,
                background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
                boxShadow: '0 4px 12px rgba(0,123,255,0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #0056b3 0%, #004085 100%)',
                  boxShadow: '0 6px 16px rgba(0,123,255,0.4)',
                  transform: 'translateY(-1px)'
                },
                '&:disabled': {
                  background: '#6c757d',
                  boxShadow: 'none'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              {loading ? 'Salvataggio...' : mode === 'add' ? 'Salva Cavo' : 'Aggiorna Cavo'}
            </Button>
          </Tooltip>
        </Box>
      </Box>
    </Paper>
  );
};

export default CavoForm;
