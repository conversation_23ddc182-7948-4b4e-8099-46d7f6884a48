from sqlalchemy import Column, Integer, String, <PERSON><PERSON><PERSON>, Date, ForeignKey
from sqlalchemy.orm import relationship

from backend.database import Base

class User(Base):
    """
    Modello SQLAlchemy per la tabella utenti.
    Corrisponde alla tabella utenti nel database esistente.
    """
    __tablename__ = "utenti"

    id_utente = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    password = Column(String, nullable=False)
    password_plain = Column(String, nullable=True)  # Campo per memorizzare la password in chiaro
    ruolo = Column(String, nullable=False)
    data_scadenza = Column(Date, nullable=True)
    abilitato = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("utenti.id_utente"), nullable=True)

    # Relazioni
    cantieri = relationship("Cantiere", back_populates="utente")
    created_users = relationship("User",
                               backref="creator",
                               remote_side=[id_utente])
