from pydantic import BaseModel, Field
from typing import Optional
from datetime import date, datetime


class CertificazioneCavoBase(BaseModel):
    """Schema base per le certificazioni dei cavi."""
    id_cavo: str = Field(..., description="ID del cavo certificato")
    id_operatore: Optional[str] = Field(None, description="Operatore che ha eseguito i test")
    strumento_utilizzato: Optional[str] = Field(None, description="Descrizione dello strumento utilizzato")
    id_strumento: Optional[int] = Field(None, description="ID dello strumento certificato")
    lunghezza_misurata: Optional[float] = Field(None, description="Lunghezza misurata in metri")
    valore_continuita: Optional[str] = Field("OK", description="Valore del test di continuità")
    valore_isolamento: Optional[str] = Field("500", description="Valore del test di isolamento in MΩ")
    valore_resistenza: Optional[str] = Field("OK", description="Valore del test di resistenza")
    note: Optional[str] = Field(None, description="Note aggiuntive")


class CertificazioneCavoCreate(CertificazioneCavoBase):
    """Schema per la creazione di una nuova certificazione."""
    pass


class CertificazioneCavoUpdate(BaseModel):
    """Schema per l'aggiornamento di una certificazione."""
    id_operatore: Optional[str] = None
    strumento_utilizzato: Optional[str] = None
    id_strumento: Optional[int] = None
    lunghezza_misurata: Optional[float] = None
    valore_continuita: Optional[str] = None
    valore_isolamento: Optional[str] = None
    valore_resistenza: Optional[str] = None
    note: Optional[str] = None


class CertificazioneCavoInDB(CertificazioneCavoBase):
    """Schema per la certificazione nel database."""
    id_certificazione: int
    id_cantiere: int
    numero_certificato: str
    data_certificazione: date
    percorso_certificato: Optional[str] = None
    percorso_foto: Optional[str] = None
    timestamp_creazione: datetime
    timestamp_modifica: Optional[datetime] = None

    class Config:
        from_attributes = True


class CertificazioneCavoResponse(CertificazioneCavoInDB):
    """Schema per la risposta API della certificazione."""
    # Informazioni aggiuntive del cavo
    cavo_tipologia: Optional[str] = None
    cavo_sezione: Optional[str] = None
    cavo_ubicazione_partenza: Optional[str] = None
    cavo_ubicazione_arrivo: Optional[str] = None
    cavo_metri_teorici: Optional[float] = None
    cavo_stato_installazione: Optional[str] = None
    
    # Informazioni dello strumento
    strumento_nome: Optional[str] = None
    strumento_marca: Optional[str] = None
    strumento_modello: Optional[str] = None


class CertificazioneCavoListResponse(BaseModel):
    """Schema per la lista delle certificazioni."""
    id_certificazione: int
    id_cavo: str
    numero_certificato: str
    data_certificazione: date
    id_operatore: Optional[str] = None
    valore_isolamento: Optional[str] = None
    strumento_utilizzato: Optional[str] = None
    cavo_tipologia: Optional[str] = None
    cavo_sezione: Optional[str] = None
    lunghezza_misurata: Optional[float] = None

    class Config:
        from_attributes = True
