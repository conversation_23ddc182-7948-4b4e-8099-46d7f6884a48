# -*- coding: utf-8 -*-
import sqlite3
import logging
from datetime import datetime
from typing import Dict, Optional
from modules.utils import ValidazioneCampi
from modules.utils import StatoInstallazione, validatore_campo
from modules.utils import StatoBobina
from typing import List, Dict, Optional

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def carica_cavi(id_cantiere: int, tipo_cavo: Optional[int] = None):
    """
    Carica tutti i cavi di un cantiere dal database, filtrando per tipo_cavo.

    :param id_cantiere: ID del cantiere.
    :param tipo_cavo: Tipo di cavo da filtrare (0 per attivi, 3 per spare).
    :return: Lista di cavi.
    """
    try:
        with sqlite3.connect('cantieri.db') as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            query = '''
                  SELECT id_cavo, utility, tipologia, n_conduttori, sezione, partenza, utenza_partenza, 
                         arrivo, utenza_arrivo, metri_teorici, metratura_reale, stato_installazione, 
                         revisione_ufficiale, modificato_manualmente, id_bobina
                  FROM Cavi 
                  WHERE id_cantiere = ?
            '''

            params = [id_cantiere]

            if tipo_cavo is not None:
                if tipo_cavo == 0:
                    query += " AND (modificato_manualmente = 0 OR modificato_manualmente = 1)"
                elif tipo_cavo == 3:
                    query += " AND modificato_manualmente = 3"

            query += " ORDER BY id_cavo"
            c.execute(query, params)
            return c.fetchall()
    except sqlite3.Error as e:
        logging.error(f"❌ Errore durante il caricamento dei cavi: {e}")
        return []


def visualizza_cavi(id_cantiere):
    """Visualizza la lista dei cavi in formato tabellare, suddivisi in attivi e spare."""
    try:
        cavi_attivi = carica_cavi(id_cantiere, tipo_cavo=0)
        print("\n=== CAVI ATTIVI ===")
        if not cavi_attivi:
            print("Nessun cavo attivo trovato.")
        else:
            _stampa_tabella_cavi(cavi_attivi)

        cavi_spare = carica_cavi(id_cantiere, tipo_cavo=3)
        print("\n=== CAVI SPARE ===")
        if not cavi_spare:
            print("Nessun cavo spare trovato.")
        else:
            _stampa_tabella_cavi(cavi_spare)
    except Exception as e:
        logging.error(f"❌ Errore durante la visualizzazione dei cavi: {e}")


def _stampa_tabella_cavi(cavi):
    """Stampa la tabella dei cavi in formato leggibile."""
    # Intestazione tabella
    print(
        "\nID Cavo         Utility         Tipologia       N.Cond    Sezione    Partenza        Arrivo          Metri T.    Metri R.    Stato           Bobina      Rev.")
    print("-" * 150)

    # Stampa i dati
    for cavo in cavi:
        try:
            # Formatta i valori per la visualizzazione
            id_cavo = str(cavo['id_cavo'])[:15].ljust(15)
            utility = str(cavo['utility'])[:15].ljust(15)
            tipologia = str(cavo['tipologia'])[:15].ljust(15)
            n_conduttori = str(cavo['n_conduttori'])[:8].ljust(10)
            sezione = str(cavo['sezione'])[:8].ljust(12)
            partenza = str(cavo['partenza'])[:15].ljust(15)
            arrivo = str(cavo['arrivo'])[:15].ljust(15)
            metri_teorici = f"{cavo['metri_teorici']:.2f}".ljust(12)
            metri_reali = f"{cavo['metratura_reale']:.2f}".ljust(12)
            stato = str(cavo['stato_installazione'])[:15].ljust(15)

            # Estrai solo la parte Y dell'ID bobina (Cx_By)
            bobina = str(cavo['id_bobina'] or "-")  # Prendi l'ID bobina o "-" se non esiste
            if bobina != "-":
                bobina = bobina.split('_B')[-1]  # Estrai solo la parte dopo "_B"
            bobina = bobina[:10].ljust(12)  # Limita a 10 caratteri e allinea

            revisione = str(cavo['revisione_ufficiale'])[:5]

            # Stampa la riga
            print(
                f"{id_cavo}{utility}{tipologia}{n_conduttori}{sezione}{partenza}{arrivo}{metri_teorici}{metri_reali}{stato}{bobina}{revisione}")

        except Exception as e:
            print(f"❌ Errore durante la stampa del cavo: {e}")


def get_revisione_corrente(id_cantiere: int) -> str:
    """
    Recupera la revisione ufficiale corrente del cantiere.
    Ritorna la revisione più recente o '00' se non ce ne sono.
    """
    try:
        with sqlite3.connect('cantieri.db') as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT revisione_ufficiale 
                FROM Cavi 
                WHERE id_cantiere = ? 
                AND revisione_ufficiale != '00'
                ORDER BY 
                    CASE 
                        WHEN revisione_ufficiale LIKE 'REV_%' THEN substr(revisione_ufficiale, 5)
                        ELSE '0'
                    END DESC
                LIMIT 1
            """, (id_cantiere,))
            result = cursor.fetchone()
            return result[0] if result else '00'
    except Exception as e:
        logging.error(f"Errore nel recupero della revisione corrente: {e}")
        return '00'


def aggiungi_cavo(id_cantiere: int) -> None:
    try:
        print("\n📝 AGGIUNTA NUOVO CAVO")

        # Recupera la revisione corrente
        revisione_corrente = get_revisione_corrente(id_cantiere)

        with sqlite3.connect('cantieri.db') as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            while True:
                id_cavo = input("ID cavo: ").strip().upper()
                if not id_cavo:
                    print("❌ L'ID cavo è obbligatorio!")
                    continue

                # Verifica esistenza cavo
                c.execute("""
                    SELECT id_cavo, stato_installazione, metratura_reale, 
                           modificato_manualmente
                    FROM Cavi 
                    WHERE id_cavo = ? AND id_cantiere = ?
                """, (id_cavo, id_cantiere))

                cavo_esistente = c.fetchone()

                if cavo_esistente:
                    # Verifica se il cavo è SPARE (modificato_manualmente = 3)
                    if cavo_esistente['modificato_manualmente'] == 3:
                        print("\n⚠️ Esiste già un cavo con questo ID marcato come SPARE.")

                        # Mostra info sullo stato attuale
                        print(f"ℹ️ Stato attuale: {cavo_esistente['stato_installazione']}")
                        if cavo_esistente['metratura_reale'] > 0:
                            print(f"ℹ️ Metratura attuale: {cavo_esistente['metratura_reale']} metri")

                        reintegra = input("Vuoi reintegrare il cavo esistente? (s/n): ").strip().lower()
                        if reintegra == 's':
                            # Determina lo stato di installazione in base alla metratura reale
                            nuovo_stato = (StatoInstallazione.INSTALLATO.value
                                           if cavo_esistente['metratura_reale'] > 0
                                           else StatoInstallazione.DA_INSTALLARE.value)

                            c.execute("""
                                UPDATE Cavi 
                                SET modificato_manualmente = 1,
                                    stato_installazione = ?,
                                    timestamp = CURRENT_TIMESTAMP
                                WHERE id_cavo = ? AND id_cantiere = ?
                            """, (nuovo_stato, id_cavo, id_cantiere))
                            conn.commit()
                            print("\n✅ Cavo reintegrato con successo!")
                            logging.info(
                                f"Cavo {id_cavo} reintegrato nel cantiere {id_cantiere} con stato '{nuovo_stato}'")
                            return
                        else:
                            print("\nInserisci un nuovo ID cavo.")
                            continue
                    else:
                        print("\n❌ Esiste già un cavo attivo con questo ID!")
                        continue

                break

            # 2. Input e validazione campi
            campi_da_validare = [
                ("utility", "Utility"),
                ("tipologia", "Tipologia"),
                ("numero_conduttori", "Numero conduttori"),
                ("sezione", "Sezione"),
                ("partenza", "Partenza"),
                ("utenza_partenza", "Utenza partenza"),
                ("arrivo", "Arrivo"),
                ("utenza_arrivo", "Utenza arrivo"),
                ("metri_teorici", "Metri teorici")
            ]

            campi = {}
            for campo_validazione, nome_campo in campi_da_validare:
                while True:
                    valore = input(f"{nome_campo}: ").strip()
                    risultato = validatore_campo(campo_validazione, valore)
                    if not risultato[0]:
                        print(f"❌ {risultato[1]}")
                        continue
                    campi[nome_campo] = risultato[2]
                    break

            # 3. Inserimento nel database
            try:
                c.execute("""
                    INSERT INTO Cavi (
                        id_cantiere, id_cavo, utility, tipologia, n_conduttori, 
                        sezione, partenza, utenza_partenza, arrivo, utenza_arrivo, 
                        metri_teorici, metratura_reale, stato_installazione, 
                        modificato_manualmente, timestamp, revisione_ufficiale
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 0, CURRENT_TIMESTAMP, ?
                    )
                """, (
                    id_cantiere, id_cavo, campi["Utility"], campi["Tipologia"],
                    campi["Numero conduttori"], campi["Sezione"], campi["Partenza"],
                    campi["Utenza partenza"], campi["Arrivo"], campi["Utenza arrivo"],
                    campi["Metri teorici"], StatoInstallazione.DA_INSTALLARE.value,
                    revisione_corrente
                ))
                conn.commit()
                print(f"\n✅ Cavo aggiunto con successo! (Revisione: {revisione_corrente})")
                logging.info(f"Nuovo cavo {id_cavo} aggiunto al cantiere {id_cantiere} con revisione {revisione_corrente}")

            except sqlite3.Error as e:
                conn.rollback()
                logging.error(f"Errore database durante l'aggiunta del cavo: {e}")
                print("\n❌ Errore durante l'inserimento del cavo nel database")

    except Exception as e:
        logging.error(f"Errore durante l'aggiunta del cavo: {e}")
        print("\n❌ Si è verificato un errore durante l'operazione")


def modifica_cavo_manualmente(id_cantiere: int) -> None:
    """
    Permette la modifica manuale di un cavo. Si possono modificare solo i dati fisici del cavo.
    I metri reali (metratura_reale) non possono essere modificati qui.
    Per modificare la bobina di un cavo posato, usare modifica_bobina_cavo_posato().

    Args:
        id_cantiere: ID del cantiere a cui appartiene il cavo.
    """
    try:
        with sqlite3.connect('cantieri.db') as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            id_cavo = input("\nInserisci l'ID del cavo da modificare: ").strip()
            if not id_cavo:
                print("\n❌ ID cavo non valido!")
                return

            # Recupera i dati del cavo
            c.execute("""
                SELECT *,
                       COALESCE(metratura_reale, 0) as metri_posati,
                       TRIM(UPPER(stato_installazione)) as stato_norm
                FROM Cavi 
                WHERE id_cavo = ? AND id_cantiere = ?
            """, (id_cavo, id_cantiere))

            cavo = c.fetchone()
            if not cavo:
                print("\n❌ Cavo non trovato!")
                return

            # Log dello stato attuale per debug
            print(f"\nℹ️ Stato attuale del cavo:")
            print(f"- Stato installazione (raw): '{cavo['stato_installazione']}'")
            print(f"- Stato normalizzato: '{cavo['stato_norm']}'")
            print(f"- Metri posati: {cavo['metri_posati']}")
            print(f"- Valore atteso per INSTALLATO: '{StatoInstallazione.INSTALLATO.value}'")

            # Controllo più robusto e anticipato
            if cavo['metratura_reale'] > 0:
                print("\n❌ Questo cavo risulta già posato (metratura_reale > 0)!")
                print("Per modificare la bobina di un cavo posato, usa l'opzione 'Modifica bobina cavo posato'")
                return

            if cavo['stato_installazione'].upper().strip() == StatoInstallazione.INSTALLATO.value.upper():
                print("\n❌ Questo cavo risulta già installato!")
                print("Per modificare la bobina di un cavo posato, usa l'opzione 'Modifica bobina cavo posato'")
                return

            # Visualizza i dettagli del cavo
            _visualizza_dettagli_cavo(cavo)

            # Input del campo da modificare
            print("\nCampi modificabili:")
            print("1. Tipologia")
            print("2. N° Conduttori")
            print("3. Sezione")
            print("4. Utility")
            print("5. Partenza")
            print("6. Utenza partenza")
            print("7. Arrivo")
            print("8. Utenza arrivo")
            print("9. Metri teorici")
            if not cavo['metratura_reale']:  # Solo se il cavo non è posato
                print("10. ID Bobina")

            campo = input("\nSeleziona il campo da modificare: ").strip()

            campi_validi = ['1', '2', '3', '4', '5', '6', '7', '8', '9']
            if not cavo['metratura_reale']:
                campi_validi.append('10')

            if campo not in campi_validi:
                print("\n❌ Opzione non valida!")
                return

            # Input del nuovo valore
            nuovo_valore = input("Inserisci il nuovo valore: ").strip()

            # Mapping dei campi e validazione
            campi = {
                '1': ('tipologia', nuovo_valore, lambda v: validatore_campo('tipologia', v)),
                '2': ('n_conduttori', nuovo_valore, lambda v: validatore_campo('numero_conduttori', v)),
                '3': ('sezione', nuovo_valore, lambda v: validatore_campo('sezione', v)),
                '4': ('utility', nuovo_valore, lambda v: validatore_campo('utility', v)),
                '5': ('partenza', nuovo_valore, lambda v: validatore_campo('partenza', v)),
                '6': ('utenza_partenza', nuovo_valore, lambda v: validatore_campo('utenza', v)),
                '7': ('arrivo', nuovo_valore, lambda v: validatore_campo('arrivo', v)),
                '8': ('utenza_arrivo', nuovo_valore, lambda v: validatore_campo('utenza', v)),
                '9': ('metri_teorici', nuovo_valore, ValidazioneCampi.valida_metri_teorici),
                '10': ('id_bobina', nuovo_valore, lambda v: validatore_campo('id_bobina', v))
            }

            campo_db, valore, validatore = campi[campo]
            risultato = validatore(valore)

            # Se il risultato è una tupla, estrai i valori
            if isinstance(risultato, tuple):
                valido, messaggio, valore_validato = risultato
            else:
                # Se il risultato è un oggetto con attributi, accedi direttamente
                valido = risultato.valido
                messaggio = risultato.messaggio
                valore_validato = risultato.valore

            if not valido:
                print(f"\n❌ {messaggio}")
                return

            # Se si sta modificando l'ID_BOBINA, verifica che esista
            if campo == '10':
                if valore_validato != "TBD":
                    c.execute('SELECT 1 FROM parco_cavi WHERE ID_BOBINA = ?', (valore_validato,))
                    if not c.fetchone():
                        print("\n❌ Bobina non trovata nel parco cavi!")
                        return

            # Aggiunta di un doppio controllo prima della modifica
            c.execute("""
                SELECT metratura_reale, stato_installazione
                FROM Cavi 
                WHERE id_cavo = ? AND id_cantiere = ?
                FOR UPDATE  -- Lock della riga
            """, (id_cavo, id_cantiere))

            check = c.fetchone()
            if check['metratura_reale'] > 0 or check[
                'stato_installazione'].upper().strip() == StatoInstallazione.INSTALLATO.value.upper():
                print("\n❌ Lo stato del cavo è cambiato durante l'operazione. Impossibile procedere.")
                return

            # Conferma modifica
            conferma = input(
                f"\nConfermi la modifica del campo '{campo_db}' a '{valore_validato}'? (s/n): ").strip().lower()
            if conferma != 's':
                print("\n❌ Modifica annullata.")
                return

            # Modifica il campo nel database
            _modifica_campo_cavo(conn, id_cavo, id_cantiere, campo_db, valore_validato)
            print("\n✅ Cavo aggiornato con successo!")
            logging.info(f"✅ Cavo {id_cavo} aggiornato: campo '{campo_db}' modificato a '{valore_validato}'")

    except ValueError:
        print("\n❌ Errore: inserire un numero valido per la metratura!")
        logging.error("❌ Errore: inserire un numero valido per la metratura!")
    except Exception as e:
        print(f"\n❌ Errore durante la modifica del cavo: {e}")
        logging.error(f"❌ Errore durante la modifica del cavo: {e}")


def _modifica_campo_cavo(conn, id_cavo: str, id_cantiere: int, campo_db: str, valore_validato: str):
    """
    Modifica un campo specifico di un cavo nel database con gestione delle transazioni.
    """
    cursor = conn.cursor()
    try:
        cursor.execute(f'''
            UPDATE Cavi 
            SET {campo_db} = ?, 
                modificato_manualmente = 1,
                timestamp = CURRENT_TIMESTAMP
            WHERE id_cantiere = ? AND id_cavo = ?
        ''', (valore_validato, id_cantiere, id_cavo))

        conn.commit()  # Commit della transazione

    except Exception as e:
        conn.rollback()  # Rollback in caso di errore
        raise e


def aggiorna_metri_posati(id_cantiere: int, id_cavo: str = None) -> bool:
    """
    Aggiorna i metri posati per un cavo specifico.
    Se il cavo è già posato, chiede all'utente se vuole cambiare la bobina o il cavo.
    """
    if not isinstance(id_cantiere, int) or id_cantiere <= 0:
        logging.error("❌ ID cantiere non valido")
        return False

    conn = None
    try:
        # Mostra la lista dei cavi prima di chiedere l'ID
        visualizza_cavi(id_cantiere)

        conn = sqlite3.connect('cantieri.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if not id_cavo:
            id_cavo = input("\nInserisci ID cavo: ").strip()

        # Ottieni i dettagli del cavo
        cursor.execute('''
            SELECT c.*, pc.metri_residui as bobina_metri_residui, pc.stato_bobina
            FROM Cavi c
            LEFT JOIN parco_cavi pc ON c.ID_BOBINA = pc.ID_BOBINA AND pc.id_cantiere = c.id_cantiere
            WHERE c.id_cavo = ? AND c.id_cantiere = ?
        ''', (id_cavo, id_cantiere))

        cavo = cursor.fetchone()
        if not cavo:
            print("\n❌ Cavo non trovato!")
            return False

        # Converti l'oggetto Row in un dizionario
        cavo_dict = dict(cavo)

        # Se il cavo è già posato, chiedi all'utente come procedere
        if cavo_dict['metratura_reale'] > 0:
            print("\n⚠️ ATTENZIONE: Questo cavo risulta già posato!")
            print("Opzioni disponibili:")
            print("1. Cambiare la bobina associata")
            print("2. Cambiare il cavo")
            scelta = input("\nScegli un'opzione (1/2): ").strip()

            if scelta == "1":
                # Chiama la funzione di modifica bobina
                return modifica_bobina_cavo_posato(id_cantiere, id_cavo)
            elif scelta == "2":
                # Torna al menu principale o chiede un nuovo ID cavo
                print("\n❌ Operazione annullata. Scegli un altro cavo.")
                return False
            else:
                print("\n❌ Opzione non valida. Operazione annullata.")
                return False

        # Visualizza i dettagli del cavo
        _visualizza_dettagli_cavo(cavo_dict)

        # Ottieni l'ID della bobina attuale
        id_bobina = cavo_dict.get('ID_BOBINA')
        bobina = None

        # Se il cavo ha già una bobina associata, ottieni i dettagli
        if id_bobina and id_bobina != "BOBINA_VUOTA":
            cursor.execute('SELECT * FROM parco_cavi WHERE ID_BOBINA = ? AND id_cantiere = ?', (id_bobina, id_cantiere))
            bobina_row = cursor.fetchone()
            if bobina_row:
                bobina = dict(bobina_row)

        # Se non c'è una bobina associata o l'utente vuole cambiarla
        if not id_bobina or id_bobina == "BOBINA_VUOTA" or input(
                "\nVuoi cambiare la bobina? (s/n): ").strip().lower() == 's':
            # Salva l'ID della vecchia bobina
            vecchia_bobina_id = id_bobina

            # Input della bobina
            id_bobina = input_bobina(cavo_dict, id_cantiere)

            # Se l'input è vuoto o 'q', annulla l'operazione
            if not id_bobina or id_bobina == "ANNULLA":
                conn.rollback()
                print("\n❌ Operazione annullata.")
                return False

            # Se l'utente vuole annullare l'installazione
            if id_bobina == "ANNULLA_INSTALLAZIONE":
                print("\nStai per annullare l'installazione del cavo.")
                if input("Confermi? (s/n): ").lower() == 's':
                    return annulla_installazione_cavo(id_cantiere, id_cavo, cavo_dict)
                conn.rollback()
                print("\n❌ Operazione annullata.")
                return False

            # Se non è BOBINA_VUOTA, ottieni i dettagli della bobina
            if id_bobina != "BOBINA_VUOTA":
                # Verifica se l'ID bobina è già nel formato completo
                if not id_bobina.startswith(f"C{id_cantiere}_B"):
                    id_bobina = f"C{id_cantiere}_B{id_bobina}"

                cursor.execute('SELECT * FROM parco_cavi WHERE ID_BOBINA = ? AND id_cantiere = ?',
                               (id_bobina, id_cantiere))
                bobina_row = cursor.fetchone()
                if not bobina_row:
                    print(f"\n❌ Bobina {id_bobina} non trovata!")
                    conn.rollback()
                    return False
                bobina = dict(bobina_row)

                # Verifica compatibilità tra cavo e bobina
                if (cavo_dict['tipologia'] != bobina['tipologia'] or
                        cavo_dict['n_conduttori'] != bobina['n_conduttori'] or
                        cavo_dict['sezione'] != bobina['sezione']):

                    print("\n⚠️ ATTENZIONE: Le caratteristiche non corrispondono!")
                    print("\nCaratteristiche cavo in lista:")
                    print(f"Tipologia: {cavo_dict['tipologia']}")
                    print(f"N° conduttori: {cavo_dict['n_conduttori']}")
                    print(f"Sezione: {cavo_dict['sezione']}")

                    print("\nCaratteristiche bobina selezionata:")
                    print(f"Tipologia: {bobina['tipologia']}")
                    print(f"N° conduttori: {bobina['n_conduttori']}")
                    print(f"Sezione: {bobina['sezione']}")
                    print(f"Metri residui: {bobina['metri_residui']}")

                    print("\nOpzioni:")
                    print("1. Aggiorna caratteristiche cavo in lista con quelle della bobina")
                    print("2. Seleziona un'altra bobina")

                    scelta = input("\nScegli un'opzione (1/2): ").strip()
                    if scelta == "1":
                        # Aggiorna le caratteristiche del cavo
                        cursor.execute('''
                            UPDATE Cavi 
                            SET tipologia = ?,
                                n_conduttori = ?,
                                sezione = ?,
                                modificato_manualmente = 1,
                                timestamp = CURRENT_TIMESTAMP
                            WHERE id_cavo = ? AND id_cantiere = ?
                        ''', (
                            bobina['tipologia'],
                            bobina['n_conduttori'],
                            bobina['sezione'],
                            id_cavo,
                            id_cantiere
                        ))

                        # Aggiorna il dizionario del cavo
                        cavo_dict['tipologia'] = bobina['tipologia']
                        cavo_dict['n_conduttori'] = bobina['n_conduttori']
                        cavo_dict['sezione'] = bobina['sezione']

                        print("\n✅ Caratteristiche cavo aggiornate con successo!")
                    else:
                        conn.rollback()
                        print("\n❌ Operazione annullata.")
                        return False

        # Input dei metri posati
        metri_reali = input_metri_posati(cavo_dict, bobina, id_bobina)
        if metri_reali is None:
            conn.rollback()
            return False

        # Passa vecchia_bobina_id se è stata cambiata la bobina
        if 'vecchia_bobina_id' in locals() and vecchia_bobina_id != id_bobina:
            success = update_cavo_bobina(conn, cavo_dict, bobina, id_cavo, id_cantiere, id_bobina, metri_reali,
                                         vecchia_bobina_id)
        else:
            success = update_cavo_bobina(conn, cavo_dict, bobina, id_cavo, id_cantiere, id_bobina, metri_reali)
        if success:
            conn.commit()
            return True
        else:
            conn.rollback()
            return False

    except Exception as e:
        if conn:
            conn.rollback()
        logging.error(f"❌ Errore in aggiorna_metri_posati: {str(e)}")
        print(f"\n❌ Errore durante l'aggiornamento: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()  # Chiudi la connessione per liberare il lock

def get_bobine_disponibili(id_cantiere: int, tipologia: Optional[str] = None, n_conduttori: Optional[int] = None, sezione: Optional[float] = None) -> List[Dict]:
    """
    Recupera le bobine disponibili per un cantiere, filtrando per caratteristiche e stato.
    Esclude bobine in stato OVER/TERMINATE.
    """
    try:
        with sqlite3.connect('cantieri.db') as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Filtri per caratteristiche
            filtri = []
            parametri = []

            if tipologia:
                filtri.append("tipologia = ?")
                parametri.append(tipologia)
            if n_conduttori:
                filtri.append("n_conduttori = ?")
                parametri.append(n_conduttori)
            if sezione:
                filtri.append("sezione = ?")
                parametri.append(sezione)

            # Escludi bobine OVER/TERMINATE
            filtri.append("stato_bobina NOT IN (?, ?)")
            parametri.extend([StatoBobina.OVER.value, StatoBobina.TERMINATA.value])

            parametri.append(id_cantiere)

            where_clause = " AND ".join(filtri) + (" AND " if filtri else "")

            query = f'''
                SELECT ID_BOBINA, tipologia, n_conduttori, sezione, 
                       metri_residui, stato_bobina, ubicazione_bobina
                FROM parco_cavi
                WHERE {where_clause} id_cantiere = ? AND metri_residui > 0
                ORDER BY stato_bobina, metri_residui DESC, ID_BOBINA ASC
            '''

            cursor.execute(query, parametri)
            bobine = cursor.fetchall()

            # Converti le righe in dizionari
            return [dict(bobina) for bobina in bobine]

    except Exception as e:
        logging.error(f"Errore durante il recupero delle bobine disponibili: {e}")
        return []



def verifica_stato_bobina(id_bobina: str, id_cantiere: int) -> Optional[str]:
    """
    Verifica se una bobina esiste e se è in stato OVER/TERMINATE.
    Restituisce:
    - None se la bobina non esiste.
    - Lo stato della bobina se esiste.
    """
    try:
        with sqlite3.connect('cantieri.db') as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT stato_bobina
                FROM parco_cavi
                WHERE ID_BOBINA = ? AND id_cantiere = ?
            ''', (id_bobina, id_cantiere))
            result = cursor.fetchone()
            return result[0] if result else None
    except Exception as e:
        logging.error(f"Errore durante la verifica dello stato della bobina: {e}")
        return None


def input_bobina(cavo: dict, id_cantiere: int) -> Optional[str]:
    """
    Gestisce l'input della bobina, ricostruendo l'ID completo nel formato Cx_By.
    L'utente inserisce solo la parte Y dell'ID bobina.
    Restituisce:
    - L'ID bobina completo (es. "C1_B3") se l'utente seleziona una bobina.
    - "BOBINA_VUOTA" se l'utente sceglie di non associare una bobina.
    - None se l'utente annulla l'operazione.
    """
    # Mostra le bobine disponibili
    print("\nBobine disponibili:")
    bobine = get_bobine_disponibili(
        id_cantiere,
        tipologia=cavo.get('tipologia'),
        n_conduttori=cavo.get('n_conduttori'),
        sezione=cavo.get('sezione')
    )

    if not bobine:
        print("❌ Nessuna bobina disponibile con le caratteristiche richieste.")
    else:
        # Visualizza le bobine
        for bobina in bobine:
            # Estrai solo la parte Y dell'ID_BOBINA
            id_parts = bobina['ID_BOBINA'].split('_B')
            y_part = id_parts[1] if len(id_parts) > 1 else bobina['ID_BOBINA']

            print(f"ID: {y_part}, "
                  f"Tipo: {bobina['tipologia']}, "
                  f"Cond: {bobina['n_conduttori']}, "
                  f"Sez: {bobina['sezione']}, "
                  f"Residui: {bobina['metri_residui']}, "
                  f"Stato: {bobina['stato_bobina']}, "
                  f"Ubicazione: {bobina['ubicazione_bobina']}")

    while True:
        y_input = input(
            "\nInserisci l'ID della bobina (solo la parte numerica o testuale): "
            "\n('v' per bobina vuota, 'q' per annullare): "
        ).strip().upper()

        # Opzione annulla
        if y_input == 'Q':
            return None  # Annulla l'operazione

        # Opzione bobina vuota
        if y_input == 'V':
            print("\nℹ️ Procedi con inserimento metri senza associare bobina")
            return "BOBINA_VUOTA"

        # Ricostruisci l'ID completo della bobina
        id_bobina = f"C{id_cantiere}_B{y_input}"

        # Verifica se la bobina esiste e non è in stato OVER/TERMINATA
        stato_bobina = verifica_stato_bobina(id_bobina, id_cantiere)
        if stato_bobina is None:
            print(f"❌ Bobina {y_input} non trovata!")
            continue
        if stato_bobina in [StatoBobina.OVER.value, StatoBobina.TERMINATA.value]:
            print(f"❌ La bobina {y_input} esiste ma è in stato {stato_bobina} e non può essere utilizzata.")
            continue

        # Restituisci l'ID completo della bobina
        return id_bobina


def input_metri_posati(cavo: dict, bobina: dict, id_bobina: str) -> Optional[float]:
    """Input e validazione dei metri posati."""
    while True:
        try:
            # Verifica se la bobina è in uno stato che impedisce l'operazione
            if id_bobina != "BOBINA_VUOTA" and bobina and 'stato_bobina' in bobina:
                if bobina['stato_bobina'] in [StatoBobina.OVER.value, StatoBobina.TERMINATA.value]:
                    print(f"\n❌ La bobina è in stato {bobina['stato_bobina']}. Non è possibile posare ulteriori metri.")
                    return None
                
                # Verifica metri residui
                if 'metri_residui' not in bobina or bobina['metri_residui'] <= 0:
                    print("\n❌ La bobina non ha metri disponibili!")
                    return None

            # Input metri posati
            metri_input = input("\nInserire metri posati: ").strip()
            if not metri_input:  # Se l'input è vuoto, esci dal loop
                return None

            # Conversione e validazione dell'input
            metri_reali = float(metri_input)
            if metri_reali <= 0:
                print("❌ I metri devono essere maggiori di zero!")
                continue

            # Verifica metri residui solo se stiamo usando una bobina reale
            if id_bobina != "BOBINA_VUOTA" and bobina:
                if metri_reali > bobina['metri_residui']:
                    print(
                        f"\n⚠️ ATTENZIONE: Stai cercando di posare {metri_reali} metri, ma la bobina ha solo {bobina['metri_residui']} metri disponibili!"
                    )
                    print("Questo porterà la bobina in stato OVER.")
                    if input("Vuoi continuare? (s/n): ").lower() != 's':
                        continue

            # Validazione dei metri teorici
            if not ValidazioneCampi.valida_metri_teorici(metri_reali).valido:
                print(f"❌ {ValidazioneCampi.valida_metri_teorici(metri_reali).messaggio}")
                continue

            # Ottieni i metri teorici del cavo
            try:
                metri_teorici = float(cavo['metri_teorici']) if cavo['metri_teorici'] is not None else 0.0
            except (ValueError, TypeError):
                metri_teorici = 0.0

            # Verifica che i metri posati non superino i metri teorici del cavo
            # Confronta direttamente i metri reali con i metri teorici senza sommare
            if metri_teorici > 0 and metri_reali > metri_teorici:
                print(
                    f"⚠️ Attenzione: i metri da posare ({metri_reali}) superano i metri teorici ({metri_teorici})"
                )
                if input("Vuoi continuare comunque? (s/n): ").lower() != 's':
                    continue

            return metri_reali

        except ValueError:
            print("❌ Inserire un numero valido!")
            continue
        except KeyError as e:
            print(f"❌ Errore: Chiave mancante nel dizionario: {e}")
            return None
        except Exception as e:
            print(f"❌ Errore imprevisto: {e}")
            return None

def gestisci_selezione_bobina(cavo: dict, id_cantiere: int) -> Optional[str]:
    """
    Gestisce la selezione della bobina, mostrando le opzioni del menu e chiamando input_bobina solo se necessario.
    Restituisce:
    - L'ID bobina completo se l'utente seleziona una bobina.
    - "BOBINA_VUOTA" se l'utente sceglie di non associare una bobina.
    - None se l'utente annulla l'operazione.
    """
    while True:
        print("\nOpzioni disponibili:")
        print("1. Seleziona una bobina")
        print("2. Torna alla selezione cavo")
        print("3. Esci")

        scelta = input("\nScegli un'opzione (1-3): ").strip()

        if scelta == "1":  # Seleziona una bobina
            return input_bobina(cavo, id_cantiere)

        elif scelta == "2":  # Torna alla selezione cavo
            print("\nTorni alla selezione del cavo.")
            return None

        elif scelta == "3":  # Esci
            print("\nEsci dall'operazione.")
            return None

        else:
            print("❌ Opzione non valida. Scegli tra 1, 2 o 3.")


def update_cavo_bobina(conn, cavo, bobina, id_cavo, id_cantiere, id_bobina, metri_reali, vecchia_bobina_id=None):
    """
    Aggiorna i metri posati del cavo e, se necessario, i metri residui della bobina.
    Se l'ID bobina è "BOBINA_VUOTA", non aggiorna i metri residui.
    Se esiste una vecchia bobina, restituisce i metri alla vecchia bobina prima di aggiornare la nuova.

    Args:
        conn: Connessione al database
        cavo: Dizionario con i dati del cavo
        bobina: Dizionario con i dati della bobina
        id_cavo: ID del cavo da aggiornare
        id_cantiere: ID del cantiere
        id_bobina: ID della nuova bobina
        metri_reali: Metri reali posati del cavo
        vecchia_bobina_id: ID della vecchia bobina (opzionale)

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Se la vecchia bobina non è "BOBINA_VUOTA", reintegra i metri
        if vecchia_bobina_id and vecchia_bobina_id != "BOBINA_VUOTA":
            # Estrai solo la parte Y dell'ID bobina (Cx_By)
            vecchia_bobina_y = vecchia_bobina_id.split('_B')[-1]

            # Ottieni i dati della vecchia bobina
            cursor.execute('''
                SELECT metri_residui, metri_totali FROM parco_cavi 
                WHERE ID_BOBINA = ? AND id_cantiere = ?
            ''', (vecchia_bobina_id, id_cantiere))

            vecchia_bobina = cursor.fetchone()
            if vecchia_bobina:
                metri_residui, metri_totali = vecchia_bobina
                metri_da_restituire = float(metri_reali)
                nuovi_metri_residui = float(metri_residui) + metri_da_restituire

                # Determina il nuovo stato della bobina
                if nuovi_metri_residui >= float(metri_totali):
                    nuovo_stato = StatoBobina.DISPONIBILE.value
                else:
                    nuovo_stato = StatoBobina.IN_USO.value

                # Aggiorna la vecchia bobina
                cursor.execute('''
                    UPDATE parco_cavi
                    SET metri_residui = ?,
                        stato_bobina = ?
                    WHERE ID_BOBINA = ? AND id_cantiere = ?
                ''', (nuovi_metri_residui, nuovo_stato, vecchia_bobina_id, id_cantiere))

                print(f"✅ Reintegrati {metri_da_restituire} metri alla bobina {vecchia_bobina_y}")
                print(f"   Nuovi metri residui: {nuovi_metri_residui}, Nuovo stato: {nuovo_stato}")

        # Aggiorna il cavo con i metri posati e lo stato
        cursor.execute('''
            UPDATE Cavi 
            SET metratura_reale = ?, 
                ID_BOBINA = ?, 
                stato_installazione = ?, 
                timestamp = CURRENT_TIMESTAMP
            WHERE id_cavo = ? AND id_cantiere = ?
        ''', (metri_reali, id_bobina, StatoInstallazione.INSTALLATO.value, id_cavo, id_cantiere))

        # Se la nuova bobina non è "BOBINA_VUOTA", aggiorna i metri residui
        if id_bobina != "BOBINA_VUOTA":
            # Estrai solo la parte Y dell'ID bobina (Cx_By)
            nuova_bobina_y = id_bobina.split('_B')[-1]

            # Ottieni i dati della nuova bobina
            cursor.execute('''
                SELECT metri_residui, metri_totali FROM parco_cavi 
                WHERE ID_BOBINA = ? AND id_cantiere = ?
            ''', (id_bobina, id_cantiere))

            nuova_bobina = cursor.fetchone()
            if nuova_bobina:
                metri_residui, metri_totali = nuova_bobina
                nuovi_metri_residui = float(metri_residui) - float(metri_reali)

                # Determina il nuovo stato della bobina
                if nuovi_metri_residui < 0:
                    nuovo_stato = StatoBobina.OVER.value
                elif nuovi_metri_residui == 0:
                    nuovo_stato = StatoBobina.TERMINATA.value
                elif nuovi_metri_residui < float(metri_totali):
                    nuovo_stato = StatoBobina.IN_USO.value
                else:
                    nuovo_stato = StatoBobina.DISPONIBILE.value

                # Aggiorna la nuova bobina
                cursor.execute('''
                    UPDATE parco_cavi
                    SET metri_residui = ?,
                        stato_bobina = ?
                    WHERE ID_BOBINA = ? AND id_cantiere = ?
                ''', (nuovi_metri_residui, nuovo_stato, id_bobina, id_cantiere))

                print(f"\n✅ Aggiornata bobina {nuova_bobina_y}")
                print(f"   Nuovi metri residui: {nuovi_metri_residui}, Nuovo stato: {nuovo_stato}")

        # Commit della transazione
        conn.commit()
        print(f"\n✅ Cavo {id_cavo} aggiornato con successo!")
        return True

    except Exception as e:
        # Rollback in caso di errore
        if conn:
            conn.rollback()
        # Usa cursor.execute('ROLLBACK') se conn non è disponibile ma cursor sì
        elif cursor:
            cursor.execute('ROLLBACK')
        print(f"\n❌ Errore durante l'aggiornamento: {str(e)}")
        logging.error(f"Errore in update_cavo_bobina: {str(e)}")
        return False


def _marca_cavo_come_spare(conn, id_cavo: str, id_cantiere: int, stato_precedente: str, metri_reali: float):
    """
    Marca un cavo come spare/consumato nel database con gestione delle transazioni.

    :param conn: Connessione al database.
    :param id_cavo: ID del cavo da marcare come spare.
    :param id_cantiere: ID del cantiere.
    :param stato_precedente: Stato precedente del cavo.
    :param metri_reali: Metri reali posati del cavo.
    """
    try:
        c = conn.cursor()
        c.execute('BEGIN')  # Inizia la transazione

        # Aggiorna il cavo impostando solo il flag SPARE e la nota
        c.execute('''
            UPDATE Cavi 
            SET stato_installazione = ?,
                timestamp = ?,
                modificato_manualmente = 3
            WHERE id_cavo = ? AND id_cantiere = ?
        ''', (
            "SPARE",
            datetime.now(),
            id_cavo,
            id_cantiere
        ))

        c.execute('COMMIT')  # Conferma la transazione
    except Exception as e:
        c.execute('ROLLBACK')  # Annulla la transazione in caso di errore
        raise e


def elimina_cavo(id_cantiere: int):
    """
    Marca un cavo come spare/consumato con flag 3 e aggiorna lo stato a SPARE,
    o lo elimina definitivamente se è da posare.

    :param id_cantiere: ID del cantiere a cui appartiene il cavo.
    """
    try:
        with sqlite3.connect('cantieri.db', detect_types=sqlite3.PARSE_DECLTYPES) as conn:
            c = conn.cursor()

            print("\n🗑️ SPARE/CONSUMATI")
            id_cavo = input("Inserisci l'ID del cavo da segnare come spare/consumato: ").strip()

            # Verifica se il cavo esiste e controlla il suo stato
            c.execute('''
                SELECT id_cavo, stato_installazione, metratura_reale 
                FROM Cavi 
                WHERE id_cavo = ? AND id_cantiere = ?
            ''', (id_cavo, id_cantiere))

            cavo = c.fetchone()
            if not cavo:
                print("❌ Cavo non trovato!")
                return

            # Controllo se il cavo è installato o ha metri reali posati
            is_installed = cavo[1] == "Installato" or (cavo[2] and cavo[2] > 0)

            if is_installed:
                print("\n⚠️ ATTENZIONE: Questo cavo risulta installato o parzialmente posato!")
                print(f"Stato: {cavo[1]}")
                print(f"Metri reali: {cavo[2]}")

                conferma_speciale = input(
                    "\n⚠️ Sei ASSOLUTAMENTE sicuro di voler procedere? (scrivi 'CONFERMA' per procedere): ").strip()

                if conferma_speciale != "CONFERMA":
                    print("Operazione annullata.")
                    return

                logging.warning(f"Cavo marcato come spare/consumato: ID={id_cavo}, "
                                f"Cantiere={id_cantiere}, Stato={cavo[1]}, Metri_reali={cavo[2]}")

                # Per cavi installati, procedi sempre con la marcatura SPARE
                try:
                    _marca_cavo_come_spare(conn, id_cavo, id_cantiere, cavo[1], cavo[2])
                    print("✅ Cavo marcato come spare/consumato con successo!")
                    logging.info(f"✅ Cavo marcato come spare/consumato: ID={id_cavo}, Cantiere={id_cantiere}")
                except Exception as e:
                    print(f"❌ Errore durante l'operazione: {str(e)}")
                    conn.rollback()
                    logging.error(f"❌ Errore durante la marcatura del cavo come spare/consumato: {str(e)}")
            else:
                # Per cavi da posare, offri l'opzione di eliminazione definitiva
                print("\n📋 Il cavo risulta da posare. Puoi:")
                print("1. Marcarlo come SPARE (mantenendolo nel database)")
                print("2. Eliminarlo definitivamente dal database")

                scelta = input("\nScegli l'opzione (1/2): ").strip()

                if scelta == "1":
                    conferma = input("⚠️ Confermi di voler marcare il cavo come SPARE? (s/n): ").lower()
                    if conferma != 's':
                        print("Operazione annullata.")
                        return

                    try:
                        _marca_cavo_come_spare(conn, id_cavo, id_cantiere, cavo[1], cavo[2])
                        print("✅ Cavo marcato come spare/consumato con successo!")
                        logging.info(f"✅ Cavo marcato come spare/consumato: ID={id_cavo}, Cantiere={id_cantiere}")
                    except Exception as e:
                        print(f"❌ Errore durante l'operazione: {str(e)}")
                        conn.rollback()
                        logging.error(f"❌ Errore durante la marcatura del cavo come spare/consumato: {str(e)}")

                elif scelta == "2":
                    conferma = input("⚠️ Confermi di voler ELIMINARE DEFINITIVAMENTE il cavo? (s/n): ").lower()
                    if conferma != 's':
                        print("Operazione annullata.")
                        return

                    try:
                        c.execute('DELETE FROM Cavi WHERE id_cavo = ? AND id_cantiere = ?',
                                  (id_cavo, id_cantiere))
                        conn.commit()
                        print("✅ Cavo eliminato definitivamente dal database!")
                        logging.info(f"✅ Cavo eliminato definitivamente: ID={id_cavo}, Cantiere={id_cantiere}")
                    except Exception as e:
                        print(f"❌ Errore durante l'eliminazione: {str(e)}")
                        conn.rollback()
                        logging.error(f"❌ Errore durante l'eliminazione definitiva del cavo: {str(e)}")

                else:
                    print("❌ Opzione non valida. Operazione annullata.")
                    return

    except Exception as e:
        logging.error(f"❌ Errore generico: {str(e)}")


def _visualizza_dettagli_cavo(cavo):
    """
    Visualizza i dettagli di un cavo.

    :param cavo: Record del cavo dal database (sqlite3.Row)
    """
    print("\nDettagli del cavo:")
    print("1. Tipologia:", cavo['tipologia'])
    print("2. N° Conduttori:", cavo['n_conduttori'])
    print("3. Sezione:", cavo['sezione'])
    print("4. Utility:", cavo['utility'])
    print("5. Partenza:", cavo['partenza'])
    print("6. Arrivo:", cavo['arrivo'])
    print("7. Utenza Partenza:", cavo['utenza_partenza'])
    print("8. Utenza Arrivo:", cavo['utenza_arrivo'])
    print("9. Metri Teorici:", cavo['metri_teorici'])


def visualizza_dettagli_bobina(cursor, id_bobina: str, id_cantiere: int,
                               messaggio: str = "Dettagli della bobina:") -> None:
    """
    Visualizza i dettagli di una bobina (ID, metri residui, metri totali, stato).

    :param cursor: Cursore del database.
    :param id_bobina: ID della bobina da visualizzare.
    :param id_cantiere: ID del cantiere.
    :param messaggio: Messaggio personalizzato da mostrare prima dei dettagli.
    """
    if id_bobina and id_bobina != "BOBINA_VUOTA" and id_bobina != "TBD":
        print(f"\n{messaggio}")
        cursor.execute('''
            SELECT ID_BOBINA, metri_residui, metri_totali, stato_bobina
            FROM parco_cavi
            WHERE ID_BOBINA = ? AND id_cantiere = ?
        ''', (id_bobina, id_cantiere))
        bobina = cursor.fetchone()
        if bobina:
            print(f"ID Bobina: {bobina['ID_BOBINA']}")
            print(f"Metri residui: {bobina['metri_residui']}")
            print(f"Metri totali: {bobina['metri_totali']}")
            print(f"Stato: {bobina['stato_bobina']}")
        else:
            print(f"❌ Bobina {id_bobina} non trovata nel parco cavi!")


def modifica_bobina_cavo_posato(id_cantiere: int):
    """Modifica la bobina associata a un cavo già posato."""
    try:
        with sqlite3.connect('cantieri.db') as conn:
            cursor = conn.cursor()

            # Richiedi l'ID del cavo
            id_cavo = input("\nInserisci l'ID del cavo da modificare: ").strip()

            # Ottieni i dati del cavo
            cursor.execute('''
                SELECT * FROM Cavi 
                WHERE id_cavo = ? AND id_cantiere = ?
            ''', (id_cavo, id_cantiere))

            cavo = cursor.fetchone()
            if not cavo:
                print(f"\n❌ Cavo {id_cavo} non trovato!")
                return False

            # Converti il risultato in dizionario
            cavo_dict = {}
            for idx, col in enumerate(cursor.description):
                cavo_dict[col[0]] = cavo[idx]

            # Verifica che il cavo abbia metri posati
            if not cavo_dict.get('metratura_reale'):
                print("\n❌ Questo cavo non ha metri posati registrati!")
                return False

            # Salva l'ID della vecchia bobina e i metri posati
            vecchia_bobina_id = cavo_dict.get('ID_BOBINA')
            metri_posati = float(cavo_dict['metratura_reale'])

            # Estrai solo la parte Y dell'ID bobina (Cx_By)
            if vecchia_bobina_id and vecchia_bobina_id != "BOBINA_VUOTA":
                vecchia_bobina_y = vecchia_bobina_id.split('_B')[-1]
            else:
                vecchia_bobina_y = "-"

            print(f"\nCavo: {id_cavo}")
            print(f"Bobina attuale: {vecchia_bobina_y}")
            print(f"Metri posati: {metri_posati}")

            # Input della nuova bobina
            nuova_bobina_y = input_bobina(cavo_dict, id_cantiere)
            if not nuova_bobina_y or nuova_bobina_y == "ANNULLA":
                print("\n❌ Operazione annullata.")
                return False

            # Ricostruisci l'ID completo della nuova bobina
            if nuova_bobina_y != "BOBINA_VUOTA":
                nuova_bobina_id = f"C{id_cantiere}_B{nuova_bobina_y}"
            else:
                nuova_bobina_id = "BOBINA_VUOTA"

            # Aggiorna il cavo con la nuova bobina
            return update_cavo_bobina(conn, cavo_dict, id_cavo, id_cantiere, nuova_bobina_id, metri_posati,
                                      vecchia_bobina_id)

    except Exception as e:
        print(f"\n❌ Errore durante la modifica della bobina: {str(e)}")
        logging.error(f"Errore in modifica_bobina_cavo_posato: {str(e)}")
        return False


def annulla_installazione_cavo(id_cantiere: int, id_cavo: str, cavo_dict: dict) -> bool:
    """
    Annulla l'installazione di un cavo, restituendo i metri alla bobina originale.
    """
    try:
        print("\n↩️ ANNULLAMENTO INSTALLAZIONE")
        print("\nDettagli del cavo:")
        print(f"ID Cavo: {cavo_dict['id_cavo']}")
        print(f"Metri posati: {cavo_dict['metratura_reale']}")
        print(f"ID Bobina: {cavo_dict.get('ID_BOBINA') or 'Nessuna bobina'}")

        with sqlite3.connect('cantieri.db') as conn:
            cursor = conn.cursor()

            metri_da_restituire = float(cavo_dict['metratura_reale'])
            id_bobina = cavo_dict.get('ID_BOBINA')

            # Inizia una transazione
            cursor.execute('BEGIN')

            # Restituisci i metri alla bobina originale
            if id_bobina and id_bobina != "BOBINA_VUOTA" and id_bobina != "TBD":
                # Verifica se l'ID bobina è già nel formato completo
                if not id_bobina.startswith(f"C{id_cantiere}_B"):
                    id_bobina = f"C{id_cantiere}_B{id_bobina}"

                cursor.execute('SELECT metri_residui, metri_totali FROM parco_cavi WHERE ID_BOBINA = ?',
                               (id_bobina,))
                result = cursor.fetchone()
                if result:
                    metri_residui, metri_totali = result
                    nuovi_metri_residui = metri_residui + metri_da_restituire

                    # Determina il nuovo stato della bobina
                    nuovo_stato = (StatoBobina.DISPONIBILE.value if nuovi_metri_residui >= metri_totali
                                   else StatoBobina.IN_USO.value)

                    cursor.execute('''
                        UPDATE parco_cavi
                        SET metri_residui = ?,
                            stato_bobina = ?
                        WHERE ID_BOBINA = ? AND id_cantiere = ?
                    ''', (nuovi_metri_residui, nuovo_stato, id_bobina, id_cantiere))
                else:
                    print(f"\n❌ Bobina {id_bobina} non trovata nel parco cavi!")
                    conn.rollback()
                    return False

            # Resetta il cavo
            cursor.execute('''
                UPDATE Cavi
                SET stato_installazione = ?,
                    metratura_reale = 0,
                    ID_BOBINA = NULL,
                    modificato_manualmente = 1,
                    timestamp = CURRENT_TIMESTAMP
                WHERE id_cavo = ? AND id_cantiere = ?
            ''', (StatoInstallazione.DA_INSTALLARE.value, id_cavo, id_cantiere))

            # Conferma la transazione
            conn.commit()
            print("\n✅ Installazione annullata con successo!")
            print("Il cavo è stato reimpostato come DA_INSTALLARE")
            return True

    except Exception as e:
        logging.error(f"Errore in annulla_installazione_cavo: {str(e)}")
        print(f"\n❌ Errore durante l'annullamento dell'installazione: {str(e)}")
        conn.rollback()  # Annulla la transazione in caso di errore
        return False
