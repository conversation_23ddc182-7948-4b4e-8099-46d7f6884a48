import React, { useState, useEffect } from 'react';
import { Box, Typography, Chip, TableRow, TableCell } from '@mui/material';
import FilterableTable from '../common/FilterableTable';
import { formatDate } from '../../utils/dateUtils';

/**
 * Componente per visualizzare la lista dei cavi con filtri in stile Excel
 *
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.cavi - Lista dei cavi da visualizzare
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano
 * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche
 */
const CaviFilterableTable = ({ cavi = [], loading = false, onFilteredDataChange = null, revisioneCorrente = null }) => {
  const [filteredCavi, setFilteredCavi] = useState(cavi);

  // Aggiorna i dati filtrati quando cambiano i cavi
  useEffect(() => {
    setFilteredCavi(cavi);
  }, [cavi]);

  // Notifica il componente padre quando cambiano i dati filtrati
  const handleFilteredDataChange = (data) => {
    setFilteredCavi(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };



  // Definizione delle colonne
  const columns = [
    {
      field: 'id_cavo',
      headerName: 'ID Cavo',
      dataType: 'text',
      headerStyle: { fontWeight: 'bold' }
    },
    // Colonna Revisione rimossa e spostata nella tabella delle statistiche
    {
      field: 'sistema',
      headerName: 'Sistema',
      dataType: 'text'
    },
    {
      field: 'utility',
      headerName: 'Utility',
      dataType: 'text'
    },
    {
      field: 'tipologia',
      headerName: 'Tipologia',
      dataType: 'text'
    },
    // n_conduttori field is now a spare field (kept in DB but hidden in UI)
    {
      field: 'sezione',
      headerName: 'Formazione',
      dataType: 'text',
      align: 'right',
      cellStyle: { textAlign: 'right' }
    },
    {
      field: 'metri_teorici',
      headerName: 'Metri Teorici',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
    },
    {
      field: 'metratura_reale',
      headerName: 'Metri Reali',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'
    },
    {
      field: 'stato_installazione',
      headerName: 'Stato',
      dataType: 'text',
      renderCell: (row) => {
        let color = 'default';
        if (row.stato_installazione === 'INSTALLATO') color = 'success';
        else if (row.stato_installazione === 'IN_CORSO') color = 'warning';
        else if (row.stato_installazione === 'DA_INSTALLARE') color = 'error';

        return (
          <Chip
            label={row.stato_installazione || 'N/D'}
            size="small"
            color={color}
            variant="outlined"
          />
        );
      }
    },
    {
      field: 'id_bobina',
      headerName: 'Bobina',
      dataType: 'text',
      renderCell: (row) => {
        // Gestione differenziata per null e BOBINA_VUOTA
        if (row.id_bobina === null) {
          // Per cavi non posati (id_bobina è null)
          return '-';
        } else if (row.id_bobina === 'BOBINA_VUOTA') {
          // Per cavi posati senza bobina specifica
          return 'BOBINA VUOTA';
        } else if (!row.id_bobina) {
          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)
          return '-';
        }

        // Estrai solo il numero della bobina (parte dopo '_B')
        const match = row.id_bobina.match(/_B(.+)$/);
        return match ? match[1] : row.id_bobina;
      }
    },
    {
      field: 'timestamp',
      headerName: 'Data Modifica',
      dataType: 'date',
      renderCell: (row) => formatDate(row.timestamp)
    },
    {
      field: 'collegamenti',
      headerName: 'Collegamenti',
      dataType: 'number',
      align: 'center',
      cellStyle: { textAlign: 'center' },
      renderCell: (row) => {
        let color = 'default';
        if (row.collegamenti === 2) color = 'success';
        else if (row.collegamenti === 1) color = 'warning';
        else color = 'error';

        return (
          <Chip
            label={row.collegamenti}
            size="small"
            color={color}
            variant="outlined"
          />
        );
      }
    }
  ];

  // Renderizza una riga personalizzata
  const renderRow = (row, index) => {
    // Determina il colore di sfondo in base allo stato
    let bgColor = 'inherit';
    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';
    else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';

    return (
      <TableRow
        key={index}
        sx={{
          backgroundColor: bgColor,
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            align={column.align || 'left'}
            sx={column.cellStyle}
          >
            {column.renderCell ? column.renderCell(row) : row[column.field]}
          </TableCell>
        ))}
      </TableRow>
    );
  };

  // Calcola le statistiche
  const calculateStats = () => {
    if (!filteredCavi.length) return null;

    const totalCavi = filteredCavi.length;
    const installati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').length;
    const inCorso = filteredCavi.filter(c => c.stato_installazione === 'IN_CORSO').length;
    const daInstallare = filteredCavi.filter(c => c.stato_installazione === 'DA_INSTALLARE').length;

    const metriTeoriciTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0);
    const metriRealiTotali = filteredCavi.reduce((sum, c) => sum + (c.metratura_reale || 0), 0);

    const percentualeCompletamento = totalCavi ? Math.round((installati / totalCavi) * 100) : 0;

    return {
      totalCavi,
      installati,
      inCorso,
      daInstallare,
      metriTeoriciTotali,
      metriRealiTotali,
      percentualeCompletamento
    };
  };

  const stats = calculateStats();

  return (
    <Box>
      {stats && (
        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1">
              Statistiche tabella cavi in{revisioneCorrente ? (
                <> <span style={{ fontWeight: 'bold' }}> Rev. "{revisioneCorrente}"</span></>
              ) : ''}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box>
              <Typography variant="body2" color="text.secondary">Completamento</Typography>
              <Typography variant="h6">{stats.percentualeCompletamento}%</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Installati</Typography>
              <Typography variant="h6" color="success.main">{stats.installati}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">In corso</Typography>
              <Typography variant="h6" color="warning.main">{stats.inCorso}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Da installare</Typography>
              <Typography variant="h6" color="error.main">{stats.daInstallare}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Metri teorici</Typography>
              <Typography variant="h6">{stats.metriTeoriciTotali.toFixed(1)} m</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Metri reali</Typography>
              <Typography variant="h6">{stats.metriRealiTotali.toFixed(1)} m</Typography>
            </Box>
          </Box>
        </Box>
      )}

      <FilterableTable
        data={cavi}
        columns={columns}
        onFilteredDataChange={handleFilteredDataChange}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        renderRow={renderRow}
      />
    </Box>
  );
};

export default CaviFilterableTable;
