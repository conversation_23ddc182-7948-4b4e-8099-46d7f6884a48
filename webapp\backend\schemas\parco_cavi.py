from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import date, datetime

class ParcoCavoBase(BaseModel):
    """Schema base per le bobine del parco cavi."""
    numero_bobina: str
    utility: str
    tipologia: str
    n_conduttori: str
    sezione: str
    metri_totali: float
    metri_residui: float
    stato_bobina: str
    ubicazione_bobina: Optional[str] = None
    fornitore: Optional[str] = None
    n_DDT: Optional[str] = None
    data_DDT: Optional[str | date] = None
    configurazione: Optional[str] = None

    @validator('n_conduttori', pre=True)
    def validate_n_conduttori_type(cls, v):
        if v is None or v == '':
            return '0'
        return str(v)

    @validator('sezione', pre=True)
    def validate_sezione_type(cls, v):
        if v is None or v == '':
            return '0'
        return str(v)

    @validator('data_DDT', pre=True)
    def validate_data_ddt_type(cls, v):
        if isinstance(v, date):
            return v.strftime('%Y-%m-%d')
        return v

class ParcoCavoCreate(BaseModel):
    """Schema per la creazione di una bobina.

    Implementa le stesse regole di validazione della CLI.
    """
    numero_bobina: str
    utility: str
    tipologia: str
    n_conduttori: str
    sezione: str
    metri_totali: float = Field(..., gt=0)
    ubicazione_bobina: Optional[str] = "TBD"
    fornitore: Optional[str] = "TBD"
    n_DDT: Optional[str] = "TBD"
    data_DDT: Optional[str] = None
    configurazione: Optional[str] = "s"

    @validator('metri_totali')
    def validate_metri_totali(cls, v):
        if v <= 0:
            raise ValueError('I metri totali devono essere maggiori di zero')
        return v

    @validator('n_conduttori')
    def validate_n_conduttori(cls, v):
        # Accetta 0 o stringa vuota
        if v is None or v == '':
            return '0'
        # Genera avviso se è un numero e supera 24
        try:
            if int(v) > 24:
                # Nella CLI questo genera un avviso ma non un errore
                pass
        except (ValueError, TypeError):
            # Se non è un numero, accetta comunque il valore (potrebbe essere una formazione)
            pass
        return str(v)

    @validator('sezione')
    def validate_sezione(cls, v):
        # Accetta stringa vuota
        if v is None or v == '':
            return '0'
        # Genera avviso se è un numero e supera 1000
        try:
            if float(str(v).replace(',', '.')) > 1000:
                # Nella CLI questo genera un avviso ma non un errore
                pass
        except (ValueError, TypeError):
            # Se non è un numero, accetta comunque il valore
            pass
        return str(v)

class ParcoCavoUpdate(BaseModel):
    """Schema per l'aggiornamento di una bobina."""
    numero_bobina: Optional[str] = None
    utility: Optional[str] = None
    tipologia: Optional[str] = None
    n_conduttori: Optional[str] = None
    sezione: Optional[str] = None
    metri_totali: Optional[float] = None
    metri_residui: Optional[float] = None
    stato_bobina: Optional[str] = None
    ubicazione_bobina: Optional[str] = None
    fornitore: Optional[str] = None
    n_DDT: Optional[str] = None
    data_DDT: Optional[str] = None
    configurazione: Optional[str] = None

class ParcoCavoInDB(ParcoCavoBase):
    """Schema per una bobina nel database."""
    id_bobina: str
    id_cantiere: int

    class Config:
        from_attributes = True

class CavoUtilizzoBobina(BaseModel):
    """Schema per un cavo che utilizza una bobina."""
    id_cavo: str
    tipologia: str
    n_conduttori: str
    sezione: str
    metri_teorici: float
    metratura_reale: float
    stato_installazione: str
    timestamp: datetime

class StoricoUtilizzoBobina(BaseModel):
    """Schema per lo storico di utilizzo di una bobina."""
    id_bobina: str
    numero_bobina: str
    utility: str
    tipologia: str
    n_conduttori: str
    sezione: str
    metri_totali: float
    metri_residui: float
    stato_bobina: str
    cavi: List[CavoUtilizzoBobina] = []

    class Config:
        from_attributes = True

class BobinaCompatibile(BaseModel):
    """Schema per una bobina compatibile con un cavo."""
    id_bobina: str
    numero_bobina: str
    tipologia: str
    n_conduttori: str
    sezione: str
    metri_residui: float
    stato_bobina: str

    class Config:
        from_attributes = True
