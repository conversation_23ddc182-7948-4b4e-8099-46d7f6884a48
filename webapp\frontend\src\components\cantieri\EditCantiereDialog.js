import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  IconButton,
  Divider
} from '@mui/material';
import {
  Edit as EditIcon,
  Lock as LockIcon,
  Construction as ConstructionIcon
} from '@mui/icons-material';
import cantieriService from '../../services/cantieriService';
import PasswordManagementDialog from './PasswordManagementDialog';

/**
 * Dialog per la modifica dei dati del cantiere
 * Include accesso rapido alla gestione password
 */
const EditCantiereDialog = ({
  open,
  onClose,
  cantiere,
  onCantiereUpdated = null
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  
  const [formData, setFormData] = useState({
    nome: '',
    descrizione: ''
  });

  // Inizializza i dati del form quando si apre il dialog
  useEffect(() => {
    if (open && cantiere) {
      setFormData({
        nome: cantiere.nome || '',
        descrizione: cantiere.descrizione || ''
      });
      setError('');
      setSuccess('');
    }
  }, [open, cantiere]);

  // Gestisce i cambiamenti nei campi del form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Gestisce il salvataggio delle modifiche
  const handleSave = async () => {
    // Validazioni
    if (!formData.nome.trim()) {
      setError('Il nome del cantiere è obbligatorio');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const updatedCantiere = await cantieriService.updateCantiere(
        cantiere.id_cantiere,
        {
          nome: formData.nome.trim(),
          descrizione: formData.descrizione.trim() || null
        }
      );
      
      setSuccess('Cantiere aggiornato con successo!');
      
      // Notifica il componente padre
      if (onCantiereUpdated) {
        onCantiereUpdated(updatedCantiere);
      }
      
      // Chiudi il dialog dopo un breve delay
      setTimeout(() => {
        handleClose();
      }, 1500);
      
    } catch (err) {
      console.error('Errore nell\'aggiornamento del cantiere:', err);
      setError(err.detail || 'Errore nell\'aggiornamento del cantiere');
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'apertura del dialog password
  const handleOpenPasswordDialog = () => {
    setShowPasswordDialog(true);
  };

  // Gestisce la chiusura del dialog password
  const handleClosePasswordDialog = () => {
    setShowPasswordDialog(false);
  };

  // Gestisce la chiusura del dialog principale
  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  // Gestisce il cambio password completato
  const handlePasswordChanged = () => {
    setSuccess('Password cambiata con successo!');
    // Il dialog password si chiuderà automaticamente
  };

  if (!cantiere) return null;

  return (
    <>
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ConstructionIcon />
            <Typography variant="h6">
              Modifica Cantiere
            </Typography>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              <strong>Codice Univoco:</strong> {cantiere.codice_univoco}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Data Creazione:</strong> {new Date(cantiere.data_creazione).toLocaleDateString('it-IT')}
            </Typography>
          </Box>

          <Divider sx={{ my: 2 }} />
          
          <TextField
            fullWidth
            label="Nome Cantiere"
            name="nome"
            value={formData.nome}
            onChange={handleInputChange}
            required
            sx={{ mb: 2 }}
            disabled={loading}
          />
          
          <TextField
            fullWidth
            label="Descrizione"
            name="descrizione"
            value={formData.descrizione}
            onChange={handleInputChange}
            multiline
            rows={3}
            sx={{ mb: 3 }}
            disabled={loading}
          />

          <Divider sx={{ my: 2 }} />

          {/* Sezione Gestione Password */}
          <Box sx={{ 
            p: 2, 
            bgcolor: 'background.default', 
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'divider'
          }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LockIcon fontSize="small" />
              Gestione Password
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Visualizza o modifica la password del cantiere
            </Typography>
            <Button
              variant="outlined"
              startIcon={<LockIcon />}
              onClick={handleOpenPasswordDialog}
              fullWidth
            >
              Gestisci Password
            </Button>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button 
            onClick={handleSave} 
            variant="contained"
            disabled={loading || !formData.nome.trim()}
            startIcon={<EditIcon />}
          >
            {loading ? 'Salvataggio...' : 'Salva Modifiche'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per la gestione password */}
      <PasswordManagementDialog
        open={showPasswordDialog}
        onClose={handleClosePasswordDialog}
        cantiere={cantiere}
        onPasswordChanged={handlePasswordChanged}
      />
    </>
  );
};

export default EditCantiereDialog;
