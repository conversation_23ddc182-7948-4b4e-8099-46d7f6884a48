#!/usr/bin/env python
# run_system_simple.py - Versione semplificata dello script di avvio
import subprocess
import sys
import os
import time
import signal
from pathlib import Path

def run_backend():
    """Avvia il server FastAPI (backend)"""
    print("Avvio del backend...")

    # Ottieni il percorso assoluto della directory webapp (non backend)
    webapp_dir = Path(__file__).resolve().parent
    backend_dir = webapp_dir / "backend"

    # Verifica che la directory esista
    if not backend_dir.exists():
        print(f"Errore: La directory del backend non esiste: {backend_dir}")
        return None

    # Rimani nella directory webapp per le importazioni corrette
    os.chdir(webapp_dir)

    # Comando per avviare il backend dalla directory webapp
    cmd = [sys.executable, "-m", "uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port=8001"]
    print(f"Esecuzione comando: {' '.join(cmd)}")
    print(f"Directory di lavoro: {os.getcwd()}")

    try:
        # Avvia il processo senza reindirizzare l'output
        process = subprocess.Popen(cmd)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(3)
        print("Backend avviato con successo!")
        return process
    except Exception as e:
        print(f"Errore durante l'avvio del backend: {e}")
        return None

def run_frontend():
    """Avvia il server React (frontend)"""
    print("Avvio del frontend...")

    # Ottieni il percorso assoluto della directory frontend
    webapp_dir = Path(__file__).resolve().parent
    frontend_dir = webapp_dir / "frontend"

    # Verifica che la directory esista
    if not frontend_dir.exists():
        print(f"Errore: La directory del frontend non esiste: {frontend_dir}")
        return None

    # Cambia directory al frontend
    os.chdir(frontend_dir)

    # Comando per avviare il frontend
    cmd = "npm start"
    print(f"Esecuzione comando: {cmd}")
    print(f"Directory di lavoro: {os.getcwd()}")

    try:
        # Avvia il processo senza reindirizzare l'output
        process = subprocess.Popen(cmd, shell=True)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(5)
        print("Frontend avviato con successo!")
        return process
    except Exception as e:
        print(f"Errore durante l'avvio del frontend: {e}")
        return None


def main():
    """Funzione principale"""
    print("\n=== Avvio del sistema CABLYS CABLE INSTALLATION ADVANCE SYSTEM ===\n")

    # Salva la directory corrente
    original_dir = os.getcwd()

    # Avvia il backend
    backend_process = run_backend()
    if not backend_process:
        print("Errore: Impossibile avviare il backend.")
        return

    # Torna alla directory originale
    os.chdir(original_dir)

    # Avvia il frontend
    frontend_process = run_frontend()
    if not frontend_process:
        print("Errore: Impossibile avviare il frontend.")
        # Termina il backend
        backend_process.terminate()
        return

    # Torna alla directory originale
    os.chdir(original_dir)

    print("\n=== Sistema CABLYS CABLE INSTALLATION ADVANCE SYSTEM avviato con successo! ===\n")
    print("Backend: http://localhost:8001")
    print("Frontend: http://localhost:3000")
    print("\nPremi Ctrl+C per terminare entrambi i server")

    # Gestione del segnale di interruzione
    def signal_handler(sig, frame):
        print("\nTerminazione dei server in corso...")
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        print("Server terminati. Arrivederci!")
        sys.exit(0)

    # Registra il gestore del segnale
    signal.signal(signal.SIGINT, signal_handler)

    # Mantiene il programma in esecuzione
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # Questo blocco non dovrebbe essere mai raggiunto grazie al signal_handler
        pass

if __name__ == "__main__":
    main()
