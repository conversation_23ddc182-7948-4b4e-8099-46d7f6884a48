import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  IconButton,
  InputAdornment,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Lock as LockIcon,
  Key as KeyIcon,
  ContentCopy as ContentCopyIcon
} from '@mui/icons-material';
import cantieriService from '../../services/cantieriService';
import HoldToViewButton from './HoldToViewButton';

/**
 * Dialog per la gestione delle password del cantiere
 * Permette di visualizzare e modificare la password
 */
const PasswordManagementDialog = ({
  open,
  onClose,
  cantiere,
  onPasswordChanged = null
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Stati per visualizzazione password
  const [viewPasswordData, setViewPasswordData] = useState({
    passwordAttuale: '',
    passwordRivelata: '',
    showPassword: false
  });
  
  // Stati per cambio password
  const [changePasswordData, setChangePasswordData] = useState({
    passwordAttuale: '',
    passwordNuova: '',
    confermaPassword: '',
    showCurrentPassword: false,
    showNewPassword: false,
    showConfirmPassword: false
  });

  // Reset dei dati quando si apre/chiude il dialog
  React.useEffect(() => {
    if (open) {
      setActiveTab(0);
      setError('');
      setSuccess('');
      setViewPasswordData({
        passwordAttuale: '',
        passwordRivelata: '',
        showPassword: false
      });
      setChangePasswordData({
        passwordAttuale: '',
        passwordNuova: '',
        confermaPassword: '',
        showCurrentPassword: false,
        showNewPassword: false,
        showConfirmPassword: false
      });
    }
  }, [open]);

  // Gestisce il cambio di tab
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setError('');
    setSuccess('');
  };

  // Gestisce la visualizzazione della password con verifica
  const handleViewPassword = async () => {
    if (!viewPasswordData.passwordAttuale.trim()) {
      setError('Inserisci la password attuale');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await cantieriService.verifyCantierePassword(
        cantiere.id_cantiere,
        viewPasswordData.passwordAttuale
      );

      if (response.password_corretta) {
        setViewPasswordData(prev => ({
          ...prev,
          passwordRivelata: response.password_cantiere,
          showPassword: true
        }));
        setSuccess('Password verificata correttamente');
      } else {
        setError('Password non corretta');
        setViewPasswordData(prev => ({
          ...prev,
          passwordRivelata: '',
          showPassword: false
        }));
      }
    } catch (err) {
      console.error('Errore nella verifica password:', err);
      setError(err.detail || 'Errore nella verifica della password');
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la visualizzazione diretta della password (senza verifica)
  const handleViewPasswordDirect = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await cantieriService.viewCantierePasswordDirect(
        cantiere.id_cantiere
      );

      setViewPasswordData(prev => ({
        ...prev,
        passwordRivelata: response.password_cantiere,
        showPassword: true,
        passwordAttuale: '' // Pulisce il campo password attuale
      }));
      setSuccess('Password recuperata con successo');
    } catch (err) {
      console.error('Errore nel recupero password:', err);
      if (err.detail && err.detail.includes('hashata')) {
        setError('La password è hashata e non può essere recuperata. Utilizza la funzione di cambio password.');
      } else {
        setError(err.detail || 'Errore nel recupero della password');
      }
    } finally {
      setLoading(false);
    }
  };

  // Gestisce il cambio password
  const handleChangePassword = async () => {
    // Validazioni
    if (!changePasswordData.passwordAttuale.trim()) {
      setError('Inserisci la password attuale');
      return;
    }
    
    if (!changePasswordData.passwordNuova.trim()) {
      setError('Inserisci la nuova password');
      return;
    }
    
    if (changePasswordData.passwordNuova !== changePasswordData.confermaPassword) {
      setError('Le nuove password non coincidono');
      return;
    }
    
    if (changePasswordData.passwordNuova.length < 3) {
      setError('La nuova password deve essere di almeno 3 caratteri');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const response = await cantieriService.changeCantierePassword(
        cantiere.id_cantiere,
        changePasswordData.passwordAttuale,
        changePasswordData.passwordNuova,
        changePasswordData.confermaPassword
      );
      
      if (response.success) {
        setSuccess('Password cambiata con successo!');
        setChangePasswordData({
          passwordAttuale: '',
          passwordNuova: '',
          confermaPassword: '',
          showCurrentPassword: false,
          showNewPassword: false,
          showConfirmPassword: false
        });
        
        // Notifica il componente padre
        if (onPasswordChanged) {
          onPasswordChanged();
        }
      } else {
        setError(response.message || 'Errore nel cambio password');
      }
    } catch (err) {
      console.error('Errore nel cambio password:', err);
      setError(err.detail || 'Errore nel cambio password');
    } finally {
      setLoading(false);
    }
  };

  // Copia la password negli appunti
  const handleCopyPassword = () => {
    if (viewPasswordData.passwordRivelata) {
      navigator.clipboard.writeText(viewPasswordData.passwordRivelata);
      setSuccess('Password copiata negli appunti');
    }
  };

  // Gestisce la chiusura del dialog
  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  if (!cantiere) return null;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LockIcon />
          <Typography variant="h6">
            Gestione Password - {cantiere.nome}
          </Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab 
              icon={<VisibilityIcon />} 
              label="Visualizza Password" 
              iconPosition="start"
            />
            <Tab 
              icon={<KeyIcon />} 
              label="Cambia Password" 
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* Tab Visualizza Password */}
        {activeTab === 0 && (
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Scegli come visualizzare la password del cantiere
            </Typography>

            {/* Opzione 1: Recupero diretto (per password dimenticate) */}
            <Box sx={{ mb: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ color: 'info.contrastText' }}>
                🔓 Password dimenticata?
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, color: 'info.contrastText' }}>
                Tieni premuto il pulsante per 2-3 secondi per recuperare la password
              </Typography>
              <HoldToViewButton
                onComplete={handleViewPasswordDirect}
                loading={loading}
                holdDuration={2500}
                fullWidth
                variant="contained"
                color="info"
              >
                Recupera Password
              </HoldToViewButton>
            </Box>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                OPPURE
              </Typography>
            </Divider>

            {/* Opzione 2: Verifica con password attuale */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                🔐 Verifica con password attuale
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Se ricordi la password, inseriscila per visualizzarla
              </Typography>

              <TextField
                fullWidth
                label="Password Attuale"
                type={viewPasswordData.showPassword ? 'text' : 'password'}
                value={viewPasswordData.passwordAttuale}
                onChange={(e) => setViewPasswordData(prev => ({
                  ...prev,
                  passwordAttuale: e.target.value
                }))}
                sx={{ mb: 2 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setViewPasswordData(prev => ({
                          ...prev,
                          showPassword: !prev.showPassword
                        }))}
                        edge="end"
                      >
                        {viewPasswordData.showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />

              <Button
                variant="outlined"
                onClick={handleViewPassword}
                disabled={loading || !viewPasswordData.passwordAttuale.trim()}
                startIcon={<VisibilityIcon />}
                fullWidth
              >
                {loading ? 'Verifica...' : 'Verifica e Visualizza'}
              </Button>
            </Box>

            {/* Risultato password */}
            {viewPasswordData.passwordRivelata && (
              <Box sx={{ mt: 3, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Password del Cantiere:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontFamily: 'monospace',
                      bgcolor: 'background.paper',
                      p: 1,
                      borderRadius: 1,
                      flex: 1
                    }}
                  >
                    {viewPasswordData.passwordRivelata}
                  </Typography>
                  <IconButton
                    onClick={handleCopyPassword}
                    title="Copia password"
                  >
                    <ContentCopyIcon />
                  </IconButton>
                </Box>
              </Box>
            )}
          </Box>
        )}

        {/* Tab Cambia Password */}
        {activeTab === 1 && (
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Inserisci la password attuale e la nuova password
            </Typography>
            
            <TextField
              fullWidth
              label="Password Attuale"
              type={changePasswordData.showCurrentPassword ? 'text' : 'password'}
              value={changePasswordData.passwordAttuale}
              onChange={(e) => setChangePasswordData(prev => ({
                ...prev,
                passwordAttuale: e.target.value
              }))}
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setChangePasswordData(prev => ({
                        ...prev,
                        showCurrentPassword: !prev.showCurrentPassword
                      }))}
                      edge="end"
                    >
                      {changePasswordData.showCurrentPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            
            <Divider sx={{ my: 2 }} />
            
            <TextField
              fullWidth
              label="Nuova Password"
              type={changePasswordData.showNewPassword ? 'text' : 'password'}
              value={changePasswordData.passwordNuova}
              onChange={(e) => setChangePasswordData(prev => ({
                ...prev,
                passwordNuova: e.target.value
              }))}
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setChangePasswordData(prev => ({
                        ...prev,
                        showNewPassword: !prev.showNewPassword
                      }))}
                      edge="end"
                    >
                      {changePasswordData.showNewPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            
            <TextField
              fullWidth
              label="Conferma Nuova Password"
              type={changePasswordData.showConfirmPassword ? 'text' : 'password'}
              value={changePasswordData.confermaPassword}
              onChange={(e) => setChangePasswordData(prev => ({
                ...prev,
                confermaPassword: e.target.value
              }))}
              sx={{ mb: 2 }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setChangePasswordData(prev => ({
                        ...prev,
                        showConfirmPassword: !prev.showConfirmPassword
                      }))}
                      edge="end"
                    >
                      {changePasswordData.showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="contained"
                onClick={handleChangePassword}
                disabled={loading || !changePasswordData.passwordAttuale.trim() || !changePasswordData.passwordNuova.trim()}
                startIcon={<KeyIcon />}
              >
                {loading ? 'Cambio...' : 'Cambia Password'}
              </Button>
            </Box>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose}>
          Chiudi
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PasswordManagementDialog;
